<?php

namespace App\Jobs;

use Throwable;
use App\Services\PyApi\AdService;
use App\Services\AuthService;
use App\Enums\CommonJobTypeEnum;
use Illuminate\Support\Facades\Log;

class CommonJob extends Job
{
    protected $type;
    protected $data;
    protected $time;

    /**
     * php artisan queue:work --queue=common &
     *
     * @param int $type
     * @param array $data
     * @param string $time
     */
    public function __construct(int $type, array $data, string $time)
    {
        $this->type = $type;
        $this->data = $data;
        $this->time = $time;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try {
            switch ($this->type){
                case CommonJobTypeEnum::STORE_AD_LOG:
                    (new AdService)->store($this->data, $this->time);
                    break;
                case CommonJobTypeEnum::UPDATE_AD_LOG:
                    (new AdService)->update($this->data, $this->time);
                    break;
            }
        }catch (Throwable $e){
            Log::error($e->getMessage());
        }
    }
}
