<?php

namespace App\Http\Controllers\PyApi;

use App\Enums\ApiCodeEnum;
use App\Http\Controllers\Controller;
use App\Services\AuthService;
use App\Services\PyApi\TemplateService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use App\Helpers\LogCheckHelper;

/**
 * 模板系统管理与分类
 */
class TemplateController extends Controller
{
    protected $templateService;

    public function __construct(TemplateService $templateService)
    {
        $this->templateService = $templateService;
    }

    /**
     * @ApiTitle(创建模板)
     * @ApiSummary(从项目或资源创建可复用模板)
     * @ApiMethod(POST)
     * @ApiRoute(/py-api/templates/create)
     * @ApiHeaders(name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     * @ApiParams(name="name", type="string", required=true, description="模板名称")
     * @ApiParams(name="description", type="string", required=false, description="模板描述")
     * @ApiParams(name="type", type="string", required=true, description="模板类型：project/resource/workflow")
     * @ApiParams(name="category", type="string", required=true, description="模板分类：story/image/video/music/mixed")
     * @ApiParams(name="source_type", type="string", required=true, description="来源类型：project/resource")
     * @ApiParams(name="source_id", type="int", required=true, description="来源ID")
     * @ApiParams(name="visibility", type="string", required=false, description="可见性：private/public/team")
     * @ApiParams(name="tags", type="array", required=false, description="标签数组")
     * @ApiParams(name="configuration", type="object", required=false, description="模板配置")
     * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="数据")
     * @ApiReturnParams (name="data.template_id", type="int", required=true, description="模板ID")
     * @ApiReturnParams (name="data.name", type="string", required=true, description="模板名称")
     * @ApiReturnParams (name="data.type", type="string", required=true, description="模板类型")
     * @ApiReturnParams (name="data.category", type="string", required=true, description="模板分类")
     * @ApiReturnParams (name="data.visibility", type="string", required=true, description="可见性")
     * @ApiReturnParams (name="data.author_id", type="int", required=true, description="作者ID")
     * @ApiReturnParams (name="data.usage_count", type="int", required=true, description="使用次数")
     * @ApiReturnParams (name="data.rating", type="decimal", required=true, description="评分")
     * @ApiReturnParams (name="data.created_at", type="string", required=true, description="创建时间")
     * @ApiReturnParams (name="data.preview_url", type="string", required=false, description="预览URL")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "模板创建成功",
     *   "data": {
     *     "template_id": 123,
     *     "name": "AI故事创作模板",
     *     "type": "project",
     *     "category": "story",
     *     "visibility": "public",
     *     "author_id": 456,
     *     "usage_count": 0,
     *     "rating": 0,
     *     "created_at": "2024-01-01 12:00:00",
     *     "preview_url": "https://example.com/template/123/preview"
     *   }
     * })
     */
    public function create(Request $request)
    {
        try {
            $rules = [
                'name' => 'required|string|min:2|max:100',
                'description' => 'sometimes|string|max:1000',
                'type' => 'required|string|in:project,resource,workflow',
                'category' => 'required|string|in:story,image,video,music,mixed',
                'source_type' => 'required|string|in:project,resource',
                'source_id' => 'required|integer',
                'visibility' => 'sometimes|string|in:private,public,team',
                'tags' => 'sometimes|array|max:10',
                'tags.*' => 'string|max:20',
                'configuration' => 'sometimes|array'
            ];

            $messages = [
                'name.required' => '模板名称不能为空',
                'name.min' => '模板名称至少2个字符',
                'name.max' => '模板名称不能超过100个字符',
                'type.required' => '模板类型不能为空',
                'type.in' => '模板类型必须是：project、resource、workflow之一',
                'category.required' => '模板分类不能为空',
                'category.in' => '模板分类必须是：story、image、video、music、mixed之一',
                'source_type.required' => '来源类型不能为空',
                'source_id.required' => '来源ID不能为空',
                'tags.max' => '最多支持10个标签',
                'tags.*.max' => '标签长度不能超过20个字符'
            ];

            $this->validateData($request->all(), $rules, $messages, []);

            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
            }

            $user = $authResult['user'];

            $templateData = [
                'name' => $request->name,
                'description' => $request->get('description'),
                'type' => $request->type,
                'category' => $request->category,
                'source_type' => $request->source_type,
                'source_id' => $request->source_id,
                'visibility' => $request->get('visibility', 'private'),
                'tags' => $request->get('tags', []),
                'configuration' => $request->get('configuration', [])
            ];

            $result = $this->templateService->createTemplate($user->id, $templateData);

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                return $this->successResponse($result['data'], $result['message']);
            } else {
                return $this->errorResponse($result['code'], $result['message'], $result['data']);
            }
        } catch (\Exception $e) {
            Log::error('创建模板失败', [
                'method' => __METHOD__,
                'user_id' => $user->id ?? null,
                'error_context' => LogCheckHelper::sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '创建模板失败', []);
        }
    }

    /**
     * @ApiTitle(使用模板)
     * @ApiSummary(基于模板创建新项目或资源)
     * @ApiMethod(POST)
     * @ApiRoute(/py-api/templates/{id}/use)
     * @ApiHeaders(name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     * @ApiParams(name="id", type="int", required=true, description="模板ID")
     * @ApiParams(name="name", type="string", required=true, description="新项目/资源名称")
     * @ApiParams(name="customization", type="object", required=false, description="自定义配置")
     * @ApiParams(name="apply_settings", type="boolean", required=false, description="是否应用模板设置")
     * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="数据")
     * @ApiReturnParams (name="data.template_id", type="int", required=true, description="模板ID")
     * @ApiReturnParams (name="data.created_type", type="string", required=true, description="创建类型")
     * @ApiReturnParams (name="data.created_id", type="int", required=true, description="创建的项目/资源ID")
     * @ApiReturnParams (name="data.name", type="string", required=true, description="名称")
     * @ApiReturnParams (name="data.applied_settings", type="boolean", required=true, description="是否应用了设置")
     * @ApiReturnParams (name="data.customizations_applied", type="int", required=true, description="应用的自定义数量")
     * @ApiReturnParams (name="data.created_at", type="string", required=true, description="创建时间")
     * @ApiReturnParams (name="data.redirect_url", type="string", required=false, description="重定向URL")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "模板应用成功",
     *   "data": {
     *     "template_id": 123,
     *     "created_type": "project",
     *     "created_id": 789,
     *     "name": "基于模板的新项目",
     *     "applied_settings": true,
     *     "customizations_applied": 3,
     *     "created_at": "2024-01-01 12:30:00",
     *     "redirect_url": "/projects/789"
     *   }
     * })
     */
    public function use($templateId, Request $request)
    {
        try {
            $rules = [
                'name' => 'required|string|min:2|max:100',
                'customization' => 'sometimes|array',
                'apply_settings' => 'sometimes|boolean'
            ];

            $messages = [
                'name.required' => '名称不能为空',
                'name.min' => '名称至少2个字符',
                'name.max' => '名称不能超过100个字符'
            ];

            $this->validateData($request->all(), $rules, $messages, []);

            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
            }

            $user = $authResult['user'];

            $useData = [
                'name' => $request->name,
                'customization' => $request->get('customization', []),
                'apply_settings' => $request->get('apply_settings', true)
            ];

            $result = $this->templateService->useTemplate($templateId, $user->id, $useData);

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                return $this->successResponse($result['data'], $result['message']);
            } else {
                return $this->errorResponse($result['code'], $result['message'], $result['data']);
            }
        } catch (\Exception $e) {
            Log::error('使用模板失败', [
                'method' => __METHOD__,
                'user_id' => $user->id ?? null,
                'error_context' => LogCheckHelper::sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '使用模板失败', []);
        }
    }

    /**
     * @ApiTitle(模板市场)
     * @ApiSummary(获取公开模板列表)
     * @ApiMethod(GET)
     * @ApiRoute(/py-api/templates/marketplace)
     * @ApiParams(name="category", type="string", required=false, description="分类过滤")
     * @ApiParams(name="type", type="string", required=false, description="类型过滤")
     * @ApiParams(name="sort", type="string", required=false, description="排序方式：latest/popular/rating")
     * @ApiParams(name="tags", type="string", required=false, description="标签过滤，逗号分隔")
     * @ApiParams(name="search", type="string", required=false, description="搜索关键词")
     * @ApiParams(name="page", type="int", required=false, description="页码")
     * @ApiParams(name="per_page", type="int", required=false, description="每页数量")
     *      * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="数据")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "success",
     *   "data": {
     *     "templates": [
     *       {
     *         "template_id": 123,
     *         "name": "AI故事创作模板",
     *         "description": "适合创作科幻故事的模板",
     *         "type": "project",
     *         "category": "story",
     *         "tags": ["科幻", "AI", "创作"],
     *         "author": {
     *           "user_id": 456,
     *           "username": "模板作者",
     *           "avatar": "https://example.com/avatar.jpg"
     *         },
     *         "usage_count": 156,
     *         "rating": 4.8,
     *         "review_count": 23,
     *         "preview_url": "https://example.com/template/123/preview",
     *         "created_at": "2024-01-01 12:00:00",
     *         "updated_at": "2024-01-01 15:00:00"
     *       }
     *     ],
     *     "featured_templates": [
     *       {
     *         "template_id": 789,
     *         "name": "精选模板",
     *         "category": "image",
     *         "usage_count": 500,
     *         "rating": 4.9
     *       }
     *     ],
     *     "categories": [
     *       {"name": "story", "count": 45},
     *       {"name": "image", "count": 32},
     *       {"name": "video", "count": 28}
     *     ],
     *     "pagination": {
     *       "current_page": 1,
     *       "per_page": 20,
     *       "total": 105,
     *       "last_page": 6
     *     }
     *   }
     * })
     */
    public function marketplace(Request $request)
    {
        try {
            $rules = [
                'category' => 'sometimes|string|in:story,image,video,music,mixed',
                'type' => 'sometimes|string|in:project,resource,workflow',
                'sort' => 'sometimes|string|in:latest,popular,rating,featured',
                'tags' => 'sometimes|string',
                'search' => 'sometimes|string|max:100',
                'page' => 'sometimes|integer|min:1',
                'per_page' => 'sometimes|integer|min:1|max:50'
            ];

            $this->validateData($request->all(), $rules);

            $filters = [
                'category' => $request->get('category'),
                'type' => $request->get('type'),
                'sort' => $request->get('sort', 'latest'),
                'tags' => $request->get('tags'),
                'search' => $request->get('search'),
                'page' => $request->get('page', 1),
                'per_page' => $request->get('per_page', 20)
            ];

            $result = $this->templateService->getTemplateMarketplace($filters);

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                return $this->successResponse($result['data'], $result['message']);
            } else {
                return $this->errorResponse($result['code'], $result['message'], $result['data']);
            }
        } catch (\Exception $e) {
            Log::error('获取模板市场失败', [
                'method' => __METHOD__,
                'user_id' => auth()->id(),
                'error_context' => LogCheckHelper::sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '获取模板市场失败', []);
        }
    }

    /**
     * @ApiTitle(我的模板)
     * @ApiSummary(获取用户创建的模板列表)
     * @ApiMethod(GET)
     * @ApiRoute(/py-api/templates/my-templates)
     * @ApiHeaders(name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     * @ApiParams(name="type", type="string", required=false, description="类型过滤")
     * @ApiParams(name="category", type="string", required=false, description="分类过滤")
     * @ApiParams(name="visibility", type="string", required=false, description="可见性过滤")
     * @ApiParams(name="page", type="int", required=false, description="页码")
     * @ApiParams(name="per_page", type="int", required=false, description="每页数量")
     *      * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="数据")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "success",
     *   "data": {
     *     "templates": [
     *       {
     *         "template_id": 123,
     *         "name": "我的故事模板",
     *         "type": "project",
     *         "category": "story",
     *         "visibility": "public",
     *         "usage_count": 25,
     *         "rating": 4.5,
     *         "created_at": "2024-01-01 12:00:00",
     *         "updated_at": "2024-01-01 15:00:00"
     *       }
     *     ],
     *     "statistics": {
     *       "total_templates": 8,
     *       "public_templates": 5,
     *       "private_templates": 3,
     *       "total_usage": 156,
     *       "average_rating": 4.3,
     *       "total_reviews": 45
     *     },
     *     "pagination": {
     *       "current_page": 1,
     *       "per_page": 20,
     *       "total": 8,
     *       "last_page": 1
     *     }
     *   }
     * })
     */
    public function myTemplates(Request $request)
    {
        try {
            $rules = [
                'type' => 'sometimes|string|in:project,resource,workflow',
                'category' => 'sometimes|string|in:story,image,video,music,mixed',
                'visibility' => 'sometimes|string|in:private,public,team',
                'page' => 'sometimes|integer|min:1',
                'per_page' => 'sometimes|integer|min:1|max:100'
            ];

            $this->validateData($request->all(), $rules);

            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
            }

            $user = $authResult['user'];

            $filters = [
                'type' => $request->get('type'),
                'category' => $request->get('category'),
                'visibility' => $request->get('visibility'),
                'page' => $request->get('page', 1),
                'per_page' => $request->get('per_page', 20)
            ];

            $result = $this->templateService->getUserTemplates($user->id, $filters);

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                return $this->successResponse($result['data'], $result['message']);
            } else {
                return $this->errorResponse($result['code'], $result['message'], $result['data']);
            }
        } catch (\Exception $e) {
            Log::error('获取我的模板失败', [
                'method' => __METHOD__,
                'user_id' => $user->id ?? null,
                'error_context' => LogCheckHelper::sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '获取我的模板失败', []);
        }
    }

    /**
     * @ApiTitle(获取模板详情)
     * @ApiSummary(获取模板的详细信息)
     * @ApiMethod(GET)
     * @ApiRoute(/py-api/templates/{id}/detail)
     * @ApiParams(name="id", type="int", required=true, description="模板ID")
     *      * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="数据")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "success",
     *   "data": {
     *     "template_id": 123,
     *     "name": "AI故事创作模板",
     *     "description": "详细的模板描述...",
     *     "type": "project",
     *     "category": "story",
     *     "visibility": "public",
     *     "tags": ["科幻", "AI", "创作"],
     *     "author": {
     *       "user_id": 456,
     *       "username": "模板作者",
     *       "avatar": "https://example.com/avatar.jpg",
     *       "template_count": 12
     *     },
     *     "configuration": {
     *       "default_settings": {},
     *       "customizable_fields": [],
     *       "required_fields": []
     *     },
     *     "statistics": {
     *       "usage_count": 156,
     *       "rating": 4.8,
     *       "review_count": 23,
     *       "favorite_count": 45,
     *       "download_count": 89
     *     },
     *     "preview": {
     *       "images": ["url1", "url2"],
     *       "demo_url": "https://example.com/demo"
     *     },
     *     "reviews": [
     *       {
     *         "user_id": 789,
     *         "username": "用户1",
     *         "rating": 5,
     *         "comment": "非常好用的模板",
     *         "created_at": "2024-01-01 14:00:00"
     *       }
     *     ],
     *     "created_at": "2024-01-01 12:00:00",
     *     "updated_at": "2024-01-01 15:00:00"
     *   }
     * })
     */
    public function detail(Request $request, $templateId)
    {
        try {
            $userId = auth()->id(); // 可能为null（未登录用户）
            $result = $this->templateService->getTemplateDetail($templateId, $userId);

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                return $this->successResponse($result['data'], $result['message']);
            } else {
                return $this->errorResponse($result['code'], $result['message'], $result['data']);
            }
        } catch (\Exception $e) {
            Log::error('获取模板详情失败', [
                'method' => __METHOD__,
                'user_id' => $userId ?? null,
                'error_context' => LogCheckHelper::sanitize_request_for_log(['templateId' => $templateId]),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '获取模板详情失败');
        }
    }

    /**
     * @ApiTitle(更新模板)
     * @ApiSummary(更新模板信息)
     * @ApiMethod(PUT)
     * @ApiRoute(/py-api/templates/{id})
     * @ApiHeaders(name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     * @ApiParams(name="id", type="int", required=true, description="模板ID")
     * @ApiParams(name="name", type="string", required=false, description="模板名称")
     * @ApiParams(name="description", type="string", required=false, description="模板描述")
     * @ApiParams(name="tags", type="array", required=false, description="标签数组")
     * @ApiParams(name="visibility", type="string", required=false, description="可见性：private/public/team")
     * @ApiParams(name="configuration", type="object", required=false, description="模板配置")
     * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="数据")
     * @ApiReturnParams (name="data.template_id", type="int", required=true, description="模板ID")
     * @ApiReturnParams (name="data.name", type="string", required=true, description="模板名称")
     * @ApiReturnParams (name="data.description", type="string", required=false, description="模板描述")
     * @ApiReturnParams (name="data.tags", type="array", required=false, description="标签数组")
     * @ApiReturnParams (name="data.visibility", type="string", required=true, description="可见性")
     * @ApiReturnParams (name="data.updated_at", type="string", required=true, description="更新时间")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "模板更新成功",
     *   "data": {
     *     "template_id": 123,
     *     "name": "更新后的模板名称",
     *     "description": "更新后的描述",
     *     "tags": ["新标签1", "新标签2"],
     *     "visibility": "public",
     *     "updated_at": "2024-01-01 16:00:00"
     *   }
     * })
     */
    public function update($templateId, Request $request)
    {
        try {
            $rules = [
                'name' => 'sometimes|string|min:2|max:100',
                'description' => 'sometimes|string|max:1000',
                'tags' => 'sometimes|array|max:10',
                'tags.*' => 'string|max:20',
                'visibility' => 'sometimes|string|in:private,public,team',
                'configuration' => 'sometimes|array'
            ];

            $this->validateData($request->all(), $rules);

            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
            }

            $user = $authResult['user'];
            $updateData = $request->only(['name', 'description', 'tags', 'visibility', 'configuration']);

            $result = $this->templateService->updateTemplate($templateId, $user->id, $updateData);

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                return $this->successResponse($result['data'], $result['message']);
            } else {
                return $this->errorResponse($result['code'], $result['message'], $result['data']);
            }
        } catch (\Exception $e) {
            Log::error('更新模板失败', [
                'method' => __METHOD__,
                'user_id' => $user->id ?? null,
                'error_context' => LogCheckHelper::sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '更新模板失败', []);
        }
    }

    /**
     * @ApiTitle(删除模板)
     * @ApiSummary(删除用户创建的模板)
     * @ApiMethod(DELETE)
     * @ApiRoute(/py-api/templates/{id})
     * @ApiHeaders(name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     * @ApiParams(name="id", type="int", required=true, description="模板ID")
     *      * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="数据")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "模板删除成功",
     *   "data": {
     *     "template_id": 123,
     *     "deleted_at": "2024-01-01 16:30:00"
     *   }
     * })
     */
    public function delete(Request $request, $templateId)
    {
        try {
            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
            }

            $user = $authResult['user'];
            $result = $this->templateService->deleteTemplate($templateId, $user->id);

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                return $this->successResponse($result['data'], $result['message']);
            } else {
                return $this->errorResponse($result['code'], $result['message'], $result['data']);
            }
        } catch (\Exception $e) {
            Log::error('模板删除失败', [
                'method' => __METHOD__,
                'user_id' => $user->id ?? null,
                'error_context' => LogCheckHelper::sanitize_request_for_log(['templateId' => $templateId]),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '模板删除失败');
        }
    }
}
