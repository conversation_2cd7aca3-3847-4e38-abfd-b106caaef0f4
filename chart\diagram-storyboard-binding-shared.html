<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>共享业务流程: 修改绑定角色（可复用标准流程）</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .mermaid {
            text-align: center;
        }
        .back-button {
            position: fixed;
            top: 20px;
            left: 20px;
            background: rgba(255,255,255,0.9);
            border: none;
            padding: 10px 20px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 14px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }
        .back-button:hover {
            background: white;
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }
        .description {
            background-color: #e8f4fd;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            border-left: 4px solid #2196F3;
        }
        .features {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 5px;
            margin-top: 20px;
        }
        .features h3 {
            color: #333;
            margin-bottom: 15px;
        }
        .features ul {
            list-style-type: none;
            padding-left: 0;
        }
        .features li {
            margin-bottom: 8px;
            padding-left: 20px;
            position: relative;
        }
        .features li:before {
            content: "✓";
            position: absolute;
            left: 0;
            color: #28a745;
            font-weight: bold;
        }
        .reference-box {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 5px;
            padding: 15px;
            margin: 20px 0;
        }
        .reference-box h4 {
            color: #856404;
            margin-top: 0;
        }
    </style>
</head>
<body>
    <button class="back-button" onclick="window.location.href='all-diagrams-index.html'">← 返回图表索引</button>
    
    <div class="container">
        <h1>🔄 共享业务流程: 修改绑定角色（可复用标准流程）</h1>
        
        <div class="description">
            <strong>流程说明：</strong>这是一个独立的、可复用的修改角色绑定业务流程，专门设计为供其他业务流程引用的标准化组件。当用户需要修改现有的角色绑定关系时，任何业务流程都可以通过参数化调用这个统一的修改绑定流程。支持解绑当前角色、选择新角色（现有或新建）、重新绑定等完整操作。<strong>重要说明：</strong>修改绑定关系本身不消费积分。如果用户选择创建新角色，积分消费在角色创建流程（diagram-character-creation-shared.html）中完成，创建成功后通过回调直接进行绑定，无需额外积分消费。所有UI交互统一由"Py视频创作工具前端"处理，确保用户体验的一致性。支持多种修改模式：直接替换、选择后替换、批量修改等，通过不同参数实现不同的业务表现。
        </div>

        <div class="reference-box">
            <h4>📋 引用说明</h4>
            <p><strong>本流程可被以下业务流程引用：</strong></p>
            <ul>
                <li>• 分镜编辑流程（修改分镜中的角色绑定）</li>
                <li>• 项目管理流程（批量修改项目角色绑定）</li>
                <li>• 角色管理流程（更新角色绑定关系）</li>
                <li>• 任何其他需要修改角色绑定功能的业务流程</li>
            </ul>
            <p><strong>调用方式：</strong>通过标准化的参数接口调用，支持不同的修改模式和操作范围。</p>
        </div>

        <div class="mermaid">
sequenceDiagram
    participant Caller as 调用方业务流程
    participant F as Py视频创作工具前端
    participant W as WebSocket服务
    participant A as 工具API接口服务
    participant SC as AiServiceClient
    participant RM as AI资源管理服务
    participant AI as AI平台
    participant DB as MySQL数据库
    participant R as Redis缓存
    participant E as 事件总线

    Note over Caller: 业务流程需要修改角色绑定功能

    Caller->>F: 调用修改绑定角色流程

    Note over F: 参数化配置处理
    F->>F: 弹出修改绑定角色界面
    F->>A: 获取当前绑定信息

    Note over A: 🔐 Token验证流程
    Note over A: 引用 diagram-22-python-token-validation.html
    A->>A: 解析Token验证用户权限

    alt Token验证失败
        A->>F: 返回认证失败
        F->>F: 显示登录提示
        F->>Caller: 返回失败结果
    else Token验证通过
        A->>DB: 查询用户状态和权限
        F->>A: GET /py-api/characters/bindings/{id}
        A->>DB: 查询当前绑定信息
        A->>F: 返回当前绑定配置数据
        F->>F: 渲染修改绑定界面

        Note over F: 显示当前绑定信息
        F->>F: 用户确认解绑当前角色
        F->>A: DELETE /py-api/characters/unbind
        A->>DB: 解除当前角色绑定关系
        A->>F: 返回解绑成功确认

        Note over F: 选择新角色流程
        alt 用户选择现有角色
            F->>A: GET /py-api/characters/list
            A->>F: 返回角色列表
            F->>F: 显示角色选择界面
        else 用户选择创建新角色
            F->>F: 调用角色创建罩层<br/>CharacterBindingModification.CharacterCreationModal.open({<br/>mode: 'filtered', filters: {projectType, style},<br/>callbacks: {onSuccess: bindNewCharacter}})
            Note over F: 引用 diagram-character-creation-shared.html
            Note over F: 角色创建罩层处理完整创建流程
            F->>F: 接收角色创建成功回调
        end

        F->>F: 用户确认修改参数
        F->>A: 修改绑定角色请求

        Note over A: 执行角色绑定修改
        Note over A: 无需积分消费，角色已存在或已创建完成
        A->>A: POST /py-api/characters/bind
        A->>DB: 创建新的角色绑定关系

        alt 绑定修改失败
            A->>F: 返回失败结果
            F->>Caller: 返回失败结果
        else 绑定修改成功
            A->>F: 返回修改后的绑定数据
            F->>F: 显示修改成功界面
            F->>Caller: 返回成功结果
        end
    end
        </div>

        <div class="features">
            <h3>🎯 可复用流程特性</h3>
            <ul>
                <li><strong>🔄 标准化调用接口：</strong>统一的参数化调用方式，支持不同业务场景的绑定修改需求</li>
                <li><strong>🔐 标准化Token验证：</strong>复用diagram-22-python-token-validation.html标准Token验证流程</li>
                <li><strong>🎨 UI统一处理：</strong>所有绑定修改的UI交互都由"Py视频创作工具前端"统一处理，确保用户体验一致性</li>
                <li><strong>📋 多模式支持：</strong>支持直接替换、确认后替换、批量修改、高级修改等多种调用模式</li>
                <li><strong>🔓 安全解绑流程：</strong>先安全解除当前绑定关系，确保数据一致性</li>
                <li><strong>🎭 灵活角色选择：</strong>支持选择现有角色或创建新角色进行绑定</li>
                <li><strong>🔄 复用角色创建流程：</strong>创建新角色时复用diagram-character-creation-shared.html标准流程，确保一致性</li>
                <li><strong>🔒 无积分消费设计：</strong>修改绑定关系本身不消费积分，角色创建的积分消费在创建流程中完成</li>
                <li><strong>⚡ 同步操作处理：</strong>修改绑定关系为同步数据库操作，快速响应用户操作</li>
                <li><strong>⚠️ 错误处理机制：</strong>完善的错误处理和用户提示机制</li>
                <li><strong>🔄 状态管理：</strong>完整的业务状态跟踪和数据同步</li>
                <li><strong>📊 用户偏好学习：</strong>记录用户行为，优化后续推荐</li>
                <li><strong>🎯 结果回调：</strong>标准化的成功/失败结果返回机制</li>
                <li><strong>🔧 可扩展配置：</strong>支持灵活的参数配置和功能扩展</li>
            </ul>

            <h3>📋 调用参数规范</h3>
            <div style="background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin-top: 10px;">
                <h4>🔧 调用接口</h4>
                <pre style="background-color: #e9ecef; padding: 10px; border-radius: 3px; overflow-x: auto;">
// 调用修改绑定角色流程（2025-08-07 标准版）
CharacterBindingModification.start({
    binding_id: 'binding_123',  // 当前绑定ID
    mode: 'direct|confirm|batch|advanced',
    options: {
        show_current_info: true,        // 显示当前绑定信息
        require_confirmation: false,    // 是否需要确认解绑
        allow_character_creation: true, // 允许创建新角色
        compatibility_check: true,      // 兼容性检查
        auto_bind_after_creation: true, // 创建后自动绑定
        batch_mode_limit: 10           // 批量模式限制
    },
    filters: {
        character_type: 'all|user_owned|library|recent',
        style_compatibility: true,      // 风格兼容性筛选
        project_context: 'current',     // 项目上下文
        exclude_current: true          // 排除当前绑定的角色
    },
    callbacks: {
        onSuccess: (result) => { /* 成功回调，包含新旧绑定信息 */ },
        onCancel: () => { /* 取消回调 */ },
        onError: (error) => { /* 错误回调 */ },
        onProgress: (progress) => { /* 进度回调，包含修改进度 */ },
        onUnbind: (old_binding) => { /* 解绑回调 */ },
        onBind: (new_binding) => { /* 新绑定回调 */ }
    }
});

// API接口调用示例（修改绑定专用）
// 1. 获取当前绑定信息
GET /py-api/characters/bindings/{id}
{
    "include_character_details": true,
    "include_position_info": true,
    "include_compatibility_info": true
}

// 2. 解除当前绑定
DELETE /py-api/characters/unbind
{
    "character_id": 123,
    "reason": "用户主动修改绑定",
    "preserve_history": true,           // 保留历史记录
    "cleanup_resources": false          // 不清理资源（准备重新绑定）
}

// 3. 创建新绑定（修改版）
POST /py-api/characters/bind
{
    "character_id": 456,
    "reason": "绑定修改",
    "storyboard_position_id": "pos_123",
    "binding_context": "modification",   // 修改上下文
    "previous_binding_id": "binding_123", // 前一个绑定ID
    "modification_reason": "用户更换角色", // 修改原因
    "compatibility_check": true,
    "inherit_settings": true            // 继承之前的设置
}

// 4. 批量修改绑定
POST /py-api/characters/batch-modify-bindings
{
    "modifications": [
        {
            "binding_id": "binding_123",
            "new_character_id": 456,
            "reason": "批量更新"
        }
    ],
    "global_settings": {
        "compatibility_check": true,
        "preserve_history": true
    }
}
                </pre>

                <h4>📤 返回结果格式</h4>
                <pre style="background-color: #e9ecef; padding: 10px; border-radius: 3px; overflow-x: auto;">
// 成功结果
{
    success: true,
    modification: {
        binding_id: 'binding_123',
        old_character: {
            id: 'char_old_123',
            name: '原角色名称',
            unbound_at: '2025-08-07T10:30:00Z'
        },
        new_character: {
            id: 'char_new_456',
            name: '新角色名称',
            bound_at: '2025-08-07T10:35:00Z'
        },
        modification_details: {
            reason: '用户更换角色',
            compatibility_score: 0.95,
            platform_used: 'deepseek',
            cost: 10
        }
    }
}

// 失败结果（遵循API规范格式）
{
    code: 1006,                    // 业务错误码：1006=积分不足, 401=认证失败, 422=参数验证失败, 404=资源不存在, 5002=控制器异常, 5003=服务层异常
    message: "积分不足",           // 业务错误码描述
    data: {                       // 错误详细数据（可选）
        current_binding: { ... },
        failed_step: 'unbind|select|bind',
        error_details: 'specific_error_info'
    },
    timestamp: 1640995200,        // 时间戳
    request_id: "req_abc123_def456" // 请求ID
}
                </pre>
            </div>

            <h3>🔗 引用示例</h3>
            <div style="background-color: #e8f4fd; padding: 15px; border-radius: 5px; margin-top: 10px;">
                <h4>📋 在分镜编辑流程中引用</h4>
                <p>当用户需要修改分镜中的角色绑定时，调用此流程：</p>
                <pre style="background-color: #f8f9fa; padding: 10px; border-radius: 3px;">
// 在分镜编辑流程中
Note over F: 用户点击"修改角色绑定"
F->>CharacterBindingModification: 调用修改绑定流程(confirm模式)
Note over CharacterBindingModification: 引用 diagram-storyboard-binding-shared.html
CharacterBindingModification->>F: 返回修改后的绑定数据
F->>StoryboardFlow: 更新分镜显示，继续编辑流程
                </pre>

                <h4>🔗 在项目管理流程中引用</h4>
                <p>当需要批量修改项目角色绑定时，调用此流程：</p>
                <pre style="background-color: #f8f9fa; padding: 10px; border-radius: 3px;">
// 在项目管理流程中
Note over F: 用户选择"批量修改角色绑定"
F->>CharacterBindingModification: 调用修改绑定流程(batch模式)
Note over CharacterBindingModification: 引用 diagram-storyboard-binding-shared.html
CharacterBindingModification->>F: 返回批量修改结果
F->>ProjectFlow: 更新项目角色配置，继续管理流程
                </pre>
            </div>

            <h3>📚 技术规范说明</h3>
            <div style="background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin-top: 10px;">
                <p><strong>本流程遵循以下技术规范：</strong></p>
                <ul>
                    <li><strong>C-1：</strong>AI任务调度 - 智能平台选择机制</li>
                    <li><strong>C-2：</strong>AI生成成功 - 积分确认扣取流程</li>
                    <li><strong>C-3：</strong>积分不足 - 快速验证机制</li>
                    <li><strong>C-4：</strong>AI生成失败 - 事件总线异步处理</li>
                    <li><strong>C-6：</strong>资源管理 - AI资源管理服务</li>
                    <li><strong>C-7：</strong>资源下载 - 直接下载机制</li>
                    <li><strong>C-8：</strong>作品发布 - 可选发布流程</li>
                    <li><strong>C-9：</strong>环境切换 - AiServiceClient统一调用</li>
                </ul>
                <p><em>这些规范确保了与系统其他流程的一致性和兼容性。</em></p>
            </div>
        </div>
    </div>

    <script>
        mermaid.initialize({
            startOnLoad: true,
            theme: 'default',
            securityLevel: 'loose',
            sequence: {
                diagramMarginX: 50,
                diagramMarginY: 10,
                actorMargin: 50,
                width: 150,
                height: 65,
                boxMargin: 10,
                boxTextMargin: 5,
                noteMargin: 10,
                messageMargin: 35
            }
        });
    </script>
</body>
</html>
