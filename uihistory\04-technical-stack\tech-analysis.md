# AIBRM.COM 技术栈深度分析

## 技术架构概览

### 整体架构模式
- **前后端分离**: Vue.js前端 + <PERSON><PERSON>后端
- **微服务架构**: 核心服务模块化部署
- **云原生设计**: 容器化部署，弹性扩缩容
- **AI服务集成**: 多种AI模型API集成

## 前端技术栈

### 核心框架
```json
{
  "vue": "^3.3.0",
  "vue-router": "^4.2.0",
  "pinia": "^2.1.0",
  "typescript": "^5.0.0",
  "vite": "^4.3.0"
}
```

### UI框架与组件库
```json
{
  "element-plus": "^2.3.0",
  "@element-plus/icons-vue": "^2.1.0",
  "tailwindcss": "^3.3.0",
  "sass": "^1.62.0"
}
```

### 工具库
```json
{
  "axios": "^1.4.0",
  "dayjs": "^1.11.0",
  "lodash-es": "^4.17.0",
  "mitt": "^3.0.0",
  "nprogress": "^0.2.0"
}
```

### 开发工具
```json
{
  "@vitejs/plugin-vue": "^4.2.0",
  "@vue/eslint-config-typescript": "^11.0.0",
  "eslint": "^8.40.0",
  "prettier": "^2.8.0",
  "vitest": "^0.31.0"
}
```

### 前端架构设计

#### 项目结构
```
src/
├── api/                 # API接口定义
├── assets/             # 静态资源
├── components/         # 通用组件
│   ├── common/         # 基础组件
│   ├── business/       # 业务组件
│   └── layout/         # 布局组件
├── composables/        # 组合式函数
├── directives/         # 自定义指令
├── hooks/              # 自定义钩子
├── layouts/            # 页面布局
├── pages/              # 页面组件
├── plugins/            # 插件配置
├── router/             # 路由配置
├── stores/             # 状态管理
├── styles/             # 样式文件
├── types/              # 类型定义
├── utils/              # 工具函数
└── main.ts             # 入口文件
```

#### 状态管理架构
```typescript
// stores/index.ts
export const useAppStore = defineStore('app', {
  state: () => ({
    user: null as User | null,
    theme: 'dark' as 'light' | 'dark',
    language: 'zh-CN',
    loading: false
  }),
  
  getters: {
    isAuthenticated: (state) => !!state.user,
    userPermissions: (state) => state.user?.permissions || []
  },
  
  actions: {
    async login(credentials: LoginCredentials) {
      this.loading = true
      try {
        const response = await authAPI.login(credentials)
        this.user = response.user
        localStorage.setItem('token', response.token)
      } finally {
        this.loading = false
      }
    }
  }
})
```

#### 路由配置
```typescript
// router/index.ts
const routes: RouteRecordRaw[] = [
  {
    path: '/',
    component: () => import('@/layouts/DefaultLayout.vue'),
    children: [
      {
        path: '',
        name: 'Home',
        component: () => import('@/pages/HomePage.vue')
      },
      {
        path: '/project',
        name: 'Project',
        component: () => import('@/pages/ProjectPage.vue'),
        meta: { requiresAuth: true }
      }
    ]
  },
  {
    path: '/editor',
    component: () => import('@/layouts/EditorLayout.vue'),
    children: [
      {
        path: 'story/:id?',
        name: 'StoryEditor',
        component: () => import('@/pages/StoryEditor.vue')
      },
      {
        path: 'character/:id?',
        name: 'CharacterEditor',
        component: () => import('@/pages/CharacterEditor.vue')
      }
    ]
  }
]
```

## 后端技术栈

### 核心框架
```json
{
  "laravel/framework": "^10.10",
  "php": "^8.3",
  "guzzlehttp/guzzle": "^7.0",
  "predis/predis": "^2.0"
}
```

### 数据库与缓存
```json
{
  "doctrine/dbal": "^3.6",
  "laravel/sanctum": "^3.2",
  "spatie/laravel-permission": "^5.10"
}
```

### AI服务集成
```json
{
  "openai-php/client": "^0.7",
  "google/cloud-translate": "^1.12",
  "aws/aws-sdk-php": "^3.270"
}
```

### 后端架构设计

#### 目录结构
```
app/
├── Console/            # 命令行工具
├── Events/             # 事件定义
├── Exceptions/         # 异常处理
├── Http/               # HTTP层
│   ├── Controllers/    # 控制器
│   ├── Middleware/     # 中间件
│   ├── Requests/       # 表单验证
│   └── Resources/      # API资源
├── Jobs/               # 队列任务
├── Listeners/          # 事件监听
├── Models/             # 数据模型
├── Notifications/      # 通知
├── Policies/           # 授权策略
├── Providers/          # 服务提供者
├── Rules/              # 验证规则
└── Services/           # 业务服务
```

#### API控制器设计
```php
<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\StoryRequest;
use App\Services\PyApi\StoryService;
use App\Services\PyApi\AIService;

class StoryController extends Controller
{
    public function __construct(
        private StoryService $storyService,
        private AIService $aiService
    ) {}
    
    public function store(StoryRequest $request)
    {
        $story = $this->storyService->create([
            'title' => $request->title,
            'content' => $request->content,
            'user_id' => auth()->id()
        ]);
        
        return response()->json([
            'success' => true,
            'data' => $story
        ]);
    }
    
    public function generateContent(Request $request)
    {
        $content = $this->aiService->generateStory([
            'prompt' => $request->prompt,
            'style' => $request->style,
            'length' => $request->length
        ]);
        
        return response()->json([
            'success' => true,
            'content' => $content
        ]);
    }
}
```

#### 服务层设计
```php
<?php

namespace App\Services;

use App\Models\Story;
use App\Events\StoryCreated;
use Illuminate\Support\Facades\Cache;

class StoryService
{
    public function create(array $data): Story
    {
        $story = Story::create($data);
        
        // 触发事件
        event(new StoryCreated($story));
        
        // 清除相关缓存
        Cache::tags(['stories', "user:{$data['user_id']}"])->flush();
        
        return $story;
    }
    
    public function getUserStories(int $userId, array $filters = [])
    {
        $cacheKey = "user_stories:{$userId}:" . md5(serialize($filters));
        
        return Cache::remember($cacheKey, 3600, function () use ($userId, $filters) {
            $query = Story::where('user_id', $userId);
            
            if (isset($filters['type'])) {
                $query->where('type', $filters['type']);
            }
            
            return $query->orderBy('updated_at', 'desc')->get();
        });
    }
}
```

## AI服务集成

### OpenAI集成
```php
<?php

namespace App\Services\AI;

use OpenAI\Client;

class OpenAIService
{
    private Client $client;
    
    public function __construct()
    {
        $this->client = OpenAI::client(config('services.openai.key'));
    }
    
    public function generateText(array $params): string
    {
        $response = $this->client->completions()->create([
            'model' => $params['model'] ?? 'gpt-3.5-turbo',
            'prompt' => $params['prompt'],
            'max_tokens' => $params['max_tokens'] ?? 1000,
            'temperature' => $params['temperature'] ?? 0.7
        ]);
        
        return $response->choices[0]->text;
    }
    
    public function generateImage(array $params): string
    {
        $response = $this->client->images()->create([
            'prompt' => $params['prompt'],
            'size' => $params['size'] ?? '512x512',
            'response_format' => 'url'
        ]);
        
        return $response->data[0]->url;
    }
}
```

### 多模型管理
```php
<?php

namespace App\Services\AI;

class AIModelManager
{
    private array $models = [
        'text' => [
            'gpt-4' => OpenAIService::class,
            'claude' => ClaudeService::class,
            'gemini' => GeminiService::class
        ],
        'image' => [
            'dalle' => OpenAIService::class,
            'midjourney' => MidjourneyService::class,
            'stable-diffusion' => StableDiffusionService::class
        ]
    ];
    
    public function generate(string $type, string $model, array $params)
    {
        $serviceClass = $this->models[$type][$model] ?? null;
        
        if (!$serviceClass) {
            throw new \InvalidArgumentException("Unsupported model: {$model}");
        }
        
        $service = app($serviceClass);
        
        return match($type) {
            'text' => $service->generateText($params),
            'image' => $service->generateImage($params),
            default => throw new \InvalidArgumentException("Unsupported type: {$type}")
        };
    }
}
```

## 数据库设计

### 核心表结构
```sql
-- 用户表
CREATE TABLE users (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    email_verified_at TIMESTAMP NULL,
    password VARCHAR(255) NOT NULL,
    avatar VARCHAR(255) NULL,
    subscription_type ENUM('free', 'pro', 'enterprise') DEFAULT 'free',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 项目表
CREATE TABLE projects (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT UNSIGNED NOT NULL,
    title VARCHAR(255) NOT NULL,
    description TEXT NULL,
    type ENUM('story', 'character', 'image', 'video') NOT NULL,
    status ENUM('draft', 'published', 'archived') DEFAULT 'draft',
    thumbnail VARCHAR(255) NULL,
    content JSON NOT NULL,
    metadata JSON NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_type (user_id, type),
    INDEX idx_status (status)
);

-- 故事表
CREATE TABLE stories (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    project_id BIGINT UNSIGNED NOT NULL,
    title VARCHAR(255) NOT NULL,
    content LONGTEXT NOT NULL,
    word_count INT UNSIGNED DEFAULT 0,
    language VARCHAR(10) DEFAULT 'zh-CN',
    genre VARCHAR(100) NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE,
    FULLTEXT(title, content)
);

-- 角色表
CREATE TABLE characters (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    project_id BIGINT UNSIGNED NULL,
    name VARCHAR(255) NOT NULL,
    description TEXT NULL,
    avatar VARCHAR(255) NULL,
    attributes JSON NOT NULL,
    is_public BOOLEAN DEFAULT FALSE,
    created_by BIGINT UNSIGNED NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE SET NULL,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_public (is_public),
    INDEX idx_creator (created_by)
);

-- AI生成记录表
CREATE TABLE ai_generations (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT UNSIGNED NOT NULL,
    type ENUM('text', 'image', 'audio', 'video') NOT NULL,
    model VARCHAR(100) NOT NULL,
    prompt TEXT NOT NULL,
    parameters JSON NULL,
    result_url VARCHAR(500) NULL,
    result_data JSON NULL,
    cost DECIMAL(10, 4) DEFAULT 0,
    status ENUM('pending', 'completed', 'failed') DEFAULT 'pending',
    error_message TEXT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_type (user_id, type),
    INDEX idx_status (status)
);
```

## 部署架构

### 容器化部署
```yaml
# docker-compose.yml
version: '3.8'
services:
  app:
    build: .
    ports:
      - "8000:8000"
    environment:
      - APP_ENV=production
      - DB_HOST=mysql
      - REDIS_HOST=redis
    depends_on:
      - mysql
      - redis
  
  mysql:
    image: mysql:8.0
    environment:
      MYSQL_ROOT_PASSWORD: secret
      MYSQL_DATABASE: aibrm
    volumes:
      - mysql_data:/var/lib/mysql
  
  redis:
    image: redis:7-alpine
    volumes:
      - redis_data:/data
  
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
    depends_on:
      - app

volumes:
  mysql_data:
  redis_data:
```

### CI/CD流程
```yaml
# .github/workflows/deploy.yml
name: Deploy to Production

on:
  push:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: 8.3
      - name: Install dependencies
        run: composer install
      - name: Run tests
        run: php artisan test
  
  deploy:
    needs: test
    runs-on: ubuntu-latest
    steps:
      - name: Deploy to server
        run: |
          ssh user@server 'cd /var/www/aibrm && git pull && composer install --no-dev && php artisan migrate'
```

## 性能优化策略

### 前端优化
- **代码分割**: 路由级别的懒加载
- **资源优化**: 图片压缩、CDN分发
- **缓存策略**: 浏览器缓存、Service Worker
- **包体积优化**: Tree shaking、依赖分析

### 后端优化
- **数据库优化**: 索引优化、查询缓存
- **Redis缓存**: 热点数据缓存、会话存储
- **队列处理**: 异步任务、批量处理
- **API优化**: 响应压缩、分页查询

这个技术栈为AIBRM.COM提供了强大的技术基础，支持高并发、高可用的AI创作平台。
