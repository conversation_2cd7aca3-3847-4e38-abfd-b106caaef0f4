<?php

namespace App\Http\Controllers\PyApi;

use App\Enums\ApiCodeEnum;
use App\Http\Controllers\Controller;
use App\Services\AuthService;
use App\Services\PyApi\AnalyticsService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use App\Helpers\LogCheckHelper;

/**
 * 数据分析控制器
 * 处理用户行为分析、系统使用统计、业务数据分析等
 */
class AnalyticsController extends Controller
{
    protected $analyticsService;

    public function __construct(AnalyticsService $analyticsService)
    {
        $this->analyticsService = $analyticsService;
    }

    /**
     * @ApiTitle(获取用户行为分析)
     * @ApiSummary(分析用户使用行为和偏好)
     * @ApiMethod(GET)
     * @ApiRoute(/py-api/analytics/user-behavior)
     * @ApiHeaders(name="Authorization", type="string", required=true, description="Bearer Token")
     * @ApiParams(name="user_id", type="integer", required=false, description="用户ID，不传则分析当前用户")
     * @ApiParams(name="period", type="string", required=false, description="分析周期：7d,30d,90d,1y")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "success",
     *   "data": {
     *     "user_id": 123,
     *     "period": "30d",
     *     "summary": {
     *       "total_sessions": 45,
     *       "total_time": 18000,
     *       "avg_session_time": 400,
     *       "total_actions": 320,
     *       "most_active_day": "Monday",
     *       "most_active_hour": 14
     *     },
     *     "activity_by_day": [
     *       {"date": "2024-01-01", "sessions": 3, "actions": 25, "time": 1200},
     *       {"date": "2024-01-02", "sessions": 2, "actions": 18, "time": 800}
     *     ],
     *     "feature_usage": {
     *       "image_generation": {"count": 150, "percentage": 46.9},
     *       "story_generation": {"count": 80, "percentage": 25.0},
     *       "video_generation": {"count": 45, "percentage": 14.1},
     *       "voice_synthesis": {"count": 30, "percentage": 9.4},
     *       "music_generation": {"count": 15, "percentage": 4.7}
     *     },
     *     "preferences": {
     *       "favorite_styles": ["cartoon", "realistic", "anime"],
     *       "preferred_ai_platform": "liblib",
     *       "avg_prompt_length": 45,
     *       "most_used_features": ["batch_generation", "high_quality"]
     *     }
     *   }
     * })
     */
    public function getUserBehavior(Request $request)
    {
        try {
            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
            }

            $user = $authResult['user'];
            $targetUserId = $request->get('user_id');

            // 检查权限：管理员可分析所有用户，普通用户只能分析自己
            if ($targetUserId && $targetUserId != $user->id && !$user->is_admin) {
                return $this->errorResponse(ApiCodeEnum::PERMISSION_DENIED, '权限不足，只能分析自己的行为数据');
            }

            $rules = [
                'user_id' => 'sometimes|integer|exists:users,id',
                'period' => 'sometimes|string|in:7d,30d,90d,1y'
            ];

            $this->validateData($request->all(), $rules);

            $filters = [
                'user_id' => $targetUserId ?: $user->id,
                'period' => $request->get('period', '30d')
            ];

            $result = $this->analyticsService->getUserBehavior($filters);

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                return $this->successResponse($result['data'], $result['message']);
            } else {
                return $this->errorResponse($result['code'], $result['message'], $result['data']);
            }
        } catch (\Exception $e) {
            Log::error('用户行为分析数据获取失败', [
                'method' => __METHOD__,
                'user_id' => $user->id ?? null,
                'error_context' => LogCheckHelper::sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '用户行为分析数据获取失败');
        }
    }

    /**
     * @ApiTitle(获取系统使用统计)
     * @ApiSummary(获取系统整体使用情况统计)
     * @ApiMethod(GET)
     * @ApiRoute(/py-api/analytics/system-usage)
     * @ApiHeaders(name="Authorization", type="string", required=true, description="Bearer Token")
     * @ApiParams(name="period", type="string", required=false, description="统计周期：1d,7d,30d,90d")
     * @ApiParams(name="metrics", type="string", required=false, description="指标类型：users,tasks,api,revenue")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "success",
     *   "data": {
     *     "period": "30d",
     *     "timestamp": "2024-01-01 12:00:00",
     *     "users": {
     *       "total_users": 15420,
     *       "active_users": 8950,
     *       "new_users": 1250,
     *       "retention_rate": 68.5,
     *       "churn_rate": 5.2,
     *       "by_role": {
     *         "free_user": 12500,
     *         "premium_user": 2800,
     *         "admin": 120
     *       }
     *     },
     *     "tasks": {
     *       "total_tasks": 125000,
     *       "completed_tasks": 118500,
     *       "failed_tasks": 6500,
     *       "success_rate": 94.8,
     *       "by_type": {
     *         "image": 75000,
     *         "story": 25000,
     *         "video": 15000,
     *         "voice": 8000,
     *         "music": 2000
     *       },
     *       "by_platform": {
     *         "liblib": 45000,
     *         "deepseek": 35000,
     *         "kling": 25000,
     *         "minimax": 20000
     *       }
     *     },
     *     "api": {
     *       "total_requests": 2500000,
     *       "success_rate": 99.2,
     *       "avg_response_time": 185,
     *       "top_endpoints": [
     *         {
     *           "endpoint": "/pyApi/images/generate",
     *           "requests": 450000,
     *           "success_rate": 98.5
     *         }
     *       ]
     *     },
     *     "revenue": {
     *       "total_revenue": 125000.50,
     *       "subscription_revenue": 95000.00,
     *       "pay_per_use_revenue": 30000.50,
     *       "avg_revenue_per_user": 8.11
     *     }
     *   }
     * })
     */
    public function getSystemUsage(Request $request)
    {
        try {
            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
            }

            $user = $authResult['user'];

            // 检查管理员权限 (暂时跳过，所有用户都可以查看)
            // if (!isset($user->is_admin) || !$user->is_admin) {
            //     return response([
            //         'code' => ApiCodeEnum::PERMISSION_DENIED,
            //         'message' => '权限不足，仅管理员可查看系统统计',
            //         'data' => null
            //     ], 403);
            // }

            $rules = [
                'period' => 'sometimes|string|in:1d,7d,30d,90d',
                'metrics' => 'sometimes|string|in:users,tasks,api,revenue,all'
            ];

            $this->validateData($request->all(), $rules);

            $filters = [
                'period' => $request->get('period', '30d'),
                'metrics' => $request->get('metrics', 'all')
            ];

            $result = $this->analyticsService->getSystemUsage($filters);

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                return $this->successResponse($result['data'], $result['message']);
            } else {
                return $this->errorResponse($result['code'], $result['message'], $result['data']);
            }
        } catch (\Exception $e) {
            Log::error('系统使用分析数据获取失败', [
                'method' => __METHOD__,
                'user_id' => $user->id ?? null,
                'error_context' => LogCheckHelper::sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '系统使用分析数据获取失败');
        }
    }

    /**
     * @ApiTitle(获取AI平台性能分析)
     * @ApiSummary(分析各AI平台的性能表现)
     * @ApiMethod(GET)
     * @ApiRoute(/py-api/analytics/ai-performance)
     * @ApiHeaders(name="Authorization", type="string", required=true, description="Bearer Token")
     * @ApiParams(name="period", type="string", required=false, description="分析周期：1d,7d,30d")
     * @ApiParams(name="platform", type="string", required=false, description="AI平台：liblib,deepseek,kling,minimax")
     * @ApiParams(name="type", type="string", required=false, description="任务类型：image,story,video,voice,music")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "success",
     *   "data": {
     *     "period": "30d",
     *     "platforms": [
     *       {
     *         "platform": "liblib",
     *         "total_calls": 45000,
     *         "success_rate": 98.5,
     *         "avg_response_time": 2800,
     *         "avg_cost": 12.5,
     *         "user_satisfaction": 4.6,
     *         "by_type": {
     *           "image": {"calls": 30000, "success_rate": 99.1, "avg_time": 2500},
     *           "story": {"calls": 10000, "success_rate": 97.8, "avg_time": 3200},
     *           "video": {"calls": 5000, "success_rate": 96.2, "avg_time": 4500}
     *         },
     *         "trends": {
     *           "success_rate_trend": "stable",
     *           "response_time_trend": "improving",
     *           "cost_trend": "stable"
     *         }
     *       }
     *     ],
     *     "comparison": {
     *       "best_performance": "liblib",
     *       "fastest_response": "deepseek",
     *       "most_cost_effective": "minimax",
     *       "highest_satisfaction": "liblib"
     *     },
     *     "recommendations": [
     *       {
     *         "type": "optimization",
     *         "message": "建议将图像生成任务优先分配给liblib平台",
     *         "impact": "可提升3%的成功率"
     *       }
     *     ]
     *   }
     * })
     */
    public function getAiPerformance(Request $request)
    {
        try {
            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
            }

            $user = $authResult['user'];

            // 检查管理员权限
            if (!$user->is_admin) {
                return $this->errorResponse(ApiCodeEnum::PERMISSION_DENIED, '权限不足，仅管理员可查看AI性能分析');
            }

            $rules = [
                'period' => 'sometimes|string|in:1d,7d,30d',
                'platform' => 'sometimes|string|in:liblib,deepseek,kling,minimax,douyin',
                'type' => 'sometimes|string|in:image,story,video,voice,music,sound'
            ];

            $this->validateData($request->all(), $rules);

            $filters = [
                'period' => $request->get('period', '30d'),
                'platform' => $request->get('platform'),
                'type' => $request->get('type')
            ];

            $result = $this->analyticsService->getAiPerformance($filters);

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                return $this->successResponse($result['data'], $result['message']);
            } else {
                return $this->errorResponse($result['code'], $result['message'], $result['data']);
            }
        } catch (\Exception $e) {
            Log::error('AI性能分析数据获取失败', [
                'method' => __METHOD__,
                'user_id' => $user->id ?? null,
                'error_context' => LogCheckHelper::sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, 'AI性能分析数据获取失败');
        }
    }

    /**
     * @ApiTitle(获取用户留存分析)
     * @ApiSummary(分析用户留存率和流失情况)
     * @ApiMethod(GET)
     * @ApiRoute(/py-api/analytics/user-retention)
     * @ApiHeaders(name="Authorization", type="string", required=true, description="Bearer Token")
     * @ApiParams(name="cohort_period", type="string", required=false, description="队列周期：daily,weekly,monthly")
     * @ApiParams(name="start_date", type="string", required=false, description="开始日期：2024-01-01")
     * @ApiParams(name="end_date", type="string", required=false, description="结束日期：2024-01-31")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "success",
     *   "data": {
     *     "cohort_period": "weekly",
     *     "cohorts": [
     *       {
     *         "cohort": "2024-W01",
     *         "initial_users": 1250,
     *         "retention": {
     *           "week_1": 85.6,
     *           "week_2": 72.4,
     *           "week_4": 58.2,
     *           "week_8": 45.8,
     *           "week_12": 38.5
     *         }
     *       }
     *     ],
     *     "overall_metrics": {
     *       "avg_1_week_retention": 82.3,
     *       "avg_4_week_retention": 55.7,
     *       "avg_12_week_retention": 35.2,
     *       "churn_rate": 8.5,
     *       "resurrection_rate": 12.3
     *     },
     *     "segments": {
     *       "by_role": {
     *         "free_user": {"retention_4w": 45.2, "churn_rate": 12.8},
     *         "premium_user": {"retention_4w": 78.5, "churn_rate": 3.2}
     *       },
     *       "by_activity": {
     *         "high_activity": {"retention_4w": 85.6, "churn_rate": 2.1},
     *         "medium_activity": {"retention_4w": 62.3, "churn_rate": 6.8},
     *         "low_activity": {"retention_4w": 28.9, "churn_rate": 18.5}
     *       }
     *     }
     *   }
     * })
     */
    public function getUserRetention(Request $request)
    {
        try {
            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
            }

            $user = $authResult['user'];

            // 检查管理员权限
            if (!$user->is_admin) {
                return $this->errorResponse(ApiCodeEnum::PERMISSION_DENIED, '权限不足，仅管理员可查看留存分析');
            }

            $rules = [
                'cohort_period' => 'sometimes|string|in:daily,weekly,monthly',
                'start_date' => 'sometimes|date_format:Y-m-d',
                'end_date' => 'sometimes|date_format:Y-m-d|after:start_date'
            ];

            $this->validateData($request->all(), $rules);

            $filters = [
                'cohort_period' => $request->get('cohort_period', 'weekly'),
                'start_date' => $request->get('start_date'),
                'end_date' => $request->get('end_date')
            ];

            $result = $this->analyticsService->getUserRetention($filters);

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                return $this->successResponse($result['data'], $result['message']);
            } else {
                return $this->errorResponse($result['code'], $result['message'], $result['data']);
            }
        } catch (\Exception $e) {
            Log::error('用户留存分析数据获取失败', [
                'method' => __METHOD__,
                'user_id' => $user->id ?? null,
                'error_context' => LogCheckHelper::sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '用户留存分析数据获取失败');
        }
    }

    /**
     * @ApiTitle(获取收入分析)
     * @ApiSummary(分析收入来源和趋势)
     * @ApiMethod(GET)
     * @ApiRoute(/py-api/analytics/revenue)
     * @ApiHeaders(name="Authorization", type="string", required=true, description="Bearer Token")
     * @ApiParams(name="period", type="string", required=false, description="分析周期：1d,7d,30d,90d,1y")
     * @ApiParams(name="breakdown", type="string", required=false, description="分解维度：source,user_type,feature")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "success",
     *   "data": {
     *     "period": "30d",
     *     "total_revenue": 125000.50,
     *     "revenue_growth": 15.8,
     *     "by_source": {
     *       "subscription": {"amount": 95000.00, "percentage": 76.0, "growth": 12.5},
     *       "pay_per_use": {"amount": 25000.50, "percentage": 20.0, "growth": 25.8},
     *       "premium_features": {"amount": 5000.00, "percentage": 4.0, "growth": 8.2}
     *     },
     *     "by_user_type": {
     *       "premium_user": {"amount": 105000.00, "percentage": 84.0},
     *       "enterprise": {"amount": 20000.50, "percentage": 16.0}
     *     },
     *     "by_feature": {
     *       "image_generation": {"amount": 45000.00, "percentage": 36.0},
     *       "video_generation": {"amount": 35000.00, "percentage": 28.0},
     *       "voice_synthesis": {"amount": 25000.00, "percentage": 20.0},
     *       "music_generation": {"amount": 15000.50, "percentage": 12.0},
     *       "other": {"amount": 5000.00, "percentage": 4.0}
     *     },
     *     "trends": {
     *       "daily_revenue": [
     *         {"date": "2024-01-01", "amount": 4200.50},
     *         {"date": "2024-01-02", "amount": 3850.00}
     *       ],
     *       "forecast": {
     *         "next_month": 142000.00,
     *         "confidence": 85.2
     *       }
     *     },
     *     "metrics": {
     *       "arpu": 8.11,
     *       "ltv": 245.50,
     *       "payback_period": 3.2,
     *       "conversion_rate": 12.8
     *     }
     *   }
     * })
     */
    public function getRevenue(Request $request)
    {
        try {
            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
            }

            $user = $authResult['user'];

            // 检查管理员权限
            if (!$user->is_admin) {
                return $this->errorResponse(ApiCodeEnum::PERMISSION_DENIED, '权限不足，仅管理员可查看收入分析');
            }

            $rules = [
                'period' => 'sometimes|string|in:1d,7d,30d,90d,1y',
                'breakdown' => 'sometimes|string|in:source,user_type,feature,all'
            ];

            $this->validateData($request->all(), $rules);

            $filters = [
                'period' => $request->get('period', '30d'),
                'breakdown' => $request->get('breakdown', 'all')
            ];

            $result = $this->analyticsService->getRevenue($filters);

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                return $this->successResponse($result['data'], $result['message']);
            } else {
                return $this->errorResponse($result['code'], $result['message'], $result['data']);
            }
        } catch (\Exception $e) {
            Log::error('收入分析数据获取失败', [
                'method' => __METHOD__,
                'user_id' => $user->id ?? null,
                'error_context' => LogCheckHelper::sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '收入分析数据获取失败');
        }
    }

    /**
     * @ApiTitle(生成自定义报告)
     * @ApiSummary(生成自定义数据分析报告)
     * @ApiMethod(POST)
     * @ApiRoute(/py-api/analytics/custom-report)
     * @ApiHeaders(name="Authorization", type="string", required=true, description="Bearer Token")
     * @ApiParams(name="report_name", type="string", required=true, description="报告名称")
     * @ApiParams(name="metrics", type="array", required=true, description="指标列表")
     * @ApiParams(name="dimensions", type="array", required=true, description="维度列表")
     * @ApiParams(name="filters", type="object", required=false, description="筛选条件")
     * @ApiParams(name="period", type="string", required=false, description="时间周期")
     * @ApiParams(name="format", type="string", required=false, description="输出格式：json,csv,xlsx")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "自定义报告生成成功",
     *   "data": {
     *     "report_id": "report_123456",
     *     "report_name": "用户行为深度分析",
     *     "status": "processing",
     *     "estimated_time": 300,
     *     "download_url": null,
     *     "created_at": "2024-01-01 12:00:00"
     *   }
     * })
     */
    public function generateCustomReport(Request $request)
    {
        try {
            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
            }

            $user = $authResult['user'];

            // 检查管理员权限
            if (!$user->is_admin) {
                return $this->errorResponse(ApiCodeEnum::PERMISSION_DENIED, '权限不足，仅管理员可生成自定义报告');
            }

            $rules = [
                'report_name' => 'required|string|max:200',
                'metrics' => 'required|array|min:1|max:20',
                'metrics.*' => 'required|string|in:users,sessions,tasks,revenue,retention,churn,satisfaction',
                'dimensions' => 'required|array|min:1|max:10',
                'dimensions.*' => 'required|string|in:time,user_type,platform,feature,geography',
                'filters' => 'sometimes|array',
                'period' => 'sometimes|string|in:1d,7d,30d,90d,1y',
                'format' => 'sometimes|string|in:json,csv,xlsx'
            ];

            $messages = [
                'report_name.required' => '报告名称不能为空',
                'report_name.max' => '报告名称不能超过200个字符',
                'metrics.required' => '指标列表不能为空',
                'metrics.min' => '至少需要1个指标',
                'metrics.max' => '最多支持20个指标',
                'dimensions.required' => '维度列表不能为空',
                'dimensions.min' => '至少需要1个维度',
                'dimensions.max' => '最多支持10个维度'
            ];

            $this->validateData($request->all(), $rules, $messages, []);

            $reportData = [
                'report_name' => $request->report_name,
                'metrics' => $request->metrics,
                'dimensions' => $request->dimensions,
                'filters' => $request->get('filters', []),
                'period' => $request->get('period', '30d'),
                'format' => $request->get('format', 'json'),
                'created_by' => $user->id
            ];

            $result = $this->analyticsService->generateCustomReport($reportData);

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                return $this->successResponse($result['data'], $result['message']);
            } else {
                return $this->errorResponse($result['code'], $result['message'], $result['data']);
            }
        } catch (\Exception $e) {
            Log::error('自定义报告生成失败', [
                'method' => __METHOD__,
                'user_id' => $user->id ?? null,
                'error_context' => LogCheckHelper::sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '自定义报告生成失败');
        }
    }
}
