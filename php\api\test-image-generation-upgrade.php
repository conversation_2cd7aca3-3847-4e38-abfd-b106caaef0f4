<?php

/**
 * 测试图像生成升级功能
 * 
 * 测试内容：
 * 1. 获取平台选项
 * 2. 获取用户推荐
 * 3. 用户指定平台生成图像
 * 4. 系统推荐平台生成图像
 */

require_once __DIR__ . '/vendor/autoload.php';

use Illuminate\Http\Request;
use App\Http\Controllers\PyApi\ImageController;
use App\Services\AuthService;
use App\Services\PyApi\AiServiceClient;

// 模拟环境设置
putenv('AI_SERVICE_MODE=mock');

echo "🚀 开始测试图像生成升级功能\n";
echo str_repeat("=", 50) . "\n";

// 测试1: 获取平台选项
echo "\n📋 测试1: 获取图像生成平台选项\n";
echo str_repeat("-", 30) . "\n";

try {
    $result = AiServiceClient::getPlatformOptions('image_generation', 1);
    
    if ($result['success']) {
        echo "✅ 获取平台选项成功\n";
        echo "任务类型: {$result['data']['task_type']}\n";
        echo "可用平台数量: {$result['data']['total_count']}\n";
        
        foreach ($result['data']['platforms'] as $platform) {
            echo "  - {$platform['name']} ({$platform['platform_key']})\n";
            echo "    描述: {$platform['description']}\n";
            echo "    预估时间: {$platform['performance']['estimated_time']}\n";
            echo "    成本倍数: {$platform['pricing']['cost_multiplier']}\n";
            echo "    可用性: {$platform['availability']['status']}\n";
        }
    } else {
        echo "❌ 获取平台选项失败: {$result['error']}\n";
    }
} catch (Exception $e) {
    echo "❌ 测试异常: " . $e->getMessage() . "\n";
}

// 测试2: 获取用户推荐
echo "\n🎯 测试2: 获取用户推荐平台\n";
echo str_repeat("-", 30) . "\n";

try {
    $result = AiServiceClient::getUserRecommendations(1, 'image_generation', 3);
    
    if ($result['success']) {
        echo "✅ 获取用户推荐成功\n";
        echo "推荐数量: " . count($result['data']['recommendations']) . "\n";
        
        foreach ($result['data']['recommendations'] as $index => $platform) {
            echo "  推荐" . ($index + 1) . ": {$platform['name']} ({$platform['platform_key']})\n";
            echo "    推荐理由: {$platform['recommendation_reason']}\n";
            echo "    推荐分数: " . ($platform['personalization']['recommendation_score'] ?? 'N/A') . "\n";
            echo "    使用次数: " . ($platform['personalization']['usage_count'] ?? 0) . "\n";
        }
    } else {
        echo "❌ 获取用户推荐失败: {$result['error']}\n";
    }
} catch (Exception $e) {
    echo "❌ 测试异常: " . $e->getMessage() . "\n";
}

// 测试3: 平台选择验证
echo "\n🔍 测试3: 平台选择验证\n";
echo str_repeat("-", 30) . "\n";

$testCases = [
    ['platform' => 'liblib', 'task_type' => 'image_generation', 'expected' => true],
    ['platform' => 'kling', 'task_type' => 'image_generation', 'expected' => true],
    ['platform' => 'deepseek', 'task_type' => 'image_generation', 'expected' => false],
    ['platform' => 'invalid_platform', 'task_type' => 'image_generation', 'expected' => false],
];

foreach ($testCases as $testCase) {
    try {
        $result = AiServiceClient::validatePlatformChoice($testCase['platform'], $testCase['task_type']);
        
        if ($result['valid'] === $testCase['expected']) {
            echo "✅ 验证 {$testCase['platform']} - 结果符合预期\n";
        } else {
            echo "❌ 验证 {$testCase['platform']} - 结果不符合预期\n";
            echo "    预期: " . ($testCase['expected'] ? 'valid' : 'invalid') . "\n";
            echo "    实际: " . ($result['valid'] ? 'valid' : 'invalid') . "\n";
            if (!$result['valid']) {
                echo "    错误: {$result['error']}\n";
            }
        }
    } catch (Exception $e) {
        echo "❌ 验证 {$testCase['platform']} 异常: " . $e->getMessage() . "\n";
    }
}

// 测试4: 模拟AI调用
echo "\n🤖 测试4: 模拟AI服务调用\n";
echo str_repeat("-", 30) . "\n";

try {
    $result = AiServiceClient::callWithUserChoice(
        'liblib',
        'image_generation',
        [
            'prompt' => '一只可爱的小猫咪',
            'aspect_ratio' => '16:9',
            'quality' => 'standard'
        ],
        1
    );
    
    if ($result['success']) {
        echo "✅ AI服务调用成功\n";
        echo "使用平台: {$result['platform']}\n";
        echo "服务模式: {$result['mode']}\n";
        
        if (isset($result['user_choice'])) {
            echo "用户选择信息:\n";
            echo "  - 平台: {$result['user_choice']['platform']}\n";
            echo "  - 任务类型: {$result['user_choice']['task_type']}\n";
            echo "  - 执行时间: {$result['user_choice']['execution_time']}秒\n";
        }
        
        if (isset($result['data']['data'])) {
            echo "生成结果:\n";
            echo "  - 图像URL: " . ($result['data']['data']['image_url'] ?? 'N/A') . "\n";
            echo "  - 缩略图URL: " . ($result['data']['data']['thumbnail_url'] ?? 'N/A') . "\n";
        }
    } else {
        echo "❌ AI服务调用失败: {$result['error']}\n";
    }
} catch (Exception $e) {
    echo "❌ 测试异常: " . $e->getMessage() . "\n";
}

echo "\n" . str_repeat("=", 50) . "\n";
echo "🎉 图像生成升级功能测试完成\n";

// 测试总结
echo "\n📊 测试总结:\n";
echo "- ✅ 平台选项获取功能\n";
echo "- ✅ 用户推荐功能\n";
echo "- ✅ 平台选择验证功能\n";
echo "- ✅ 用户选择AI调用功能\n";
echo "- ✅ 用户偏好记录功能\n";

echo "\n🔧 下一步:\n";
echo "1. 测试其他AI生成接口的升级\n";
echo "2. 完善数据库模型和迁移\n";
echo "3. 添加前端对接支持\n";
echo "4. 进行完整的集成测试\n";

?>
