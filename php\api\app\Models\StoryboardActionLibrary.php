<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * 分镜动作库模型
 * 
 * @property int $id
 * @property string $title
 * @property string $description
 * @property string $camera_shot
 * @property string $action_type
 * @property string $action_category
 * @property string $emotion
 * @property string $difficulty_level
 * @property string $duration_range
 * @property string $sketch_image_url
 * @property string $reference_video_url
 * @property bool $is_user_uploaded
 * @property int $uploaded_by
 * @property array $tags
 * @property int $usage_count
 * @property float $rating
 * @property bool $is_active
 * @property bool $is_featured
 * @property int $sort_order
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 */
class StoryboardActionLibrary extends Model
{
    use HasFactory;

    /**
     * 表名
     */
    protected $table = 'p_storyboard_action_library';

    /**
     * 可批量赋值的属性
     */
    protected $fillable = [
        'title',
        'description',
        'camera_shot',
        'action_type',
        'difficulty_level',
        'sketch_image_url',
        'is_user_uploaded',
        'uploaded_by',
        'tags',
        'usage_count',
        'sort_order'
    ];

    /**
     * 属性类型转换
     */
    protected $casts = [
        'tags' => 'array',
        'is_user_uploaded' => 'boolean',
        'usage_count' => 'integer',
        'sort_order' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];

    /**
     * 默认值
     */
    protected $attributes = [
        'difficulty_level' => 'medium',
        'usage_count' => 0,
        'sort_order' => 0,
        'is_user_uploaded' => false
    ];

    /**
     * 镜头类型常量
     */
    const CAMERA_SHOT_CLOSE_UP = 'close_up';
    const CAMERA_SHOT_MEDIUM_SHOT = 'medium_shot';
    const CAMERA_SHOT_LONG_SHOT = 'long_shot';
    const CAMERA_SHOT_EXTREME_CLOSE_UP = 'extreme_close_up';
    const CAMERA_SHOT_EXTREME_LONG_SHOT = 'extreme_long_shot';
    const CAMERA_SHOT_OVER_SHOULDER = 'over_shoulder';
    const CAMERA_SHOT_BIRD_EYE = 'bird_eye';
    const CAMERA_SHOT_WORM_EYE = 'worm_eye';

    /**
     * 难度等级常量
     */
    const DIFFICULTY_EASY = 'easy';
    const DIFFICULTY_MEDIUM = 'medium';
    const DIFFICULTY_HARD = 'hard';



    /**
     * 关联上传用户
     */
    public function uploader(): BelongsTo
    {
        return $this->belongsTo(User::class, 'uploaded_by');
    }

    /**
     * 关联使用此动作的项目分镜
     */
    public function storyboards(): HasMany
    {
        return $this->hasMany(ProjectStoryboard::class, 'action_library_id');
    }

    /**
     * 获取镜头类型选项
     */
    public static function getCameraShotOptions(): array
    {
        return [
            self::CAMERA_SHOT_CLOSE_UP => '特写',
            self::CAMERA_SHOT_MEDIUM_SHOT => '中景',
            self::CAMERA_SHOT_LONG_SHOT => '远景',
            self::CAMERA_SHOT_EXTREME_CLOSE_UP => '大特写',
            self::CAMERA_SHOT_EXTREME_LONG_SHOT => '大远景',
            self::CAMERA_SHOT_OVER_SHOULDER => '过肩镜头',
            self::CAMERA_SHOT_BIRD_EYE => '鸟瞰',
            self::CAMERA_SHOT_WORM_EYE => '仰视'
        ];
    }

    /**
     * 获取难度等级选项
     */
    public static function getDifficultyOptions(): array
    {
        return [
            self::DIFFICULTY_EASY => '简单',
            self::DIFFICULTY_MEDIUM => '中等',
            self::DIFFICULTY_HARD => '困难'
        ];
    }



    /**
     * 增加使用次数
     */
    public function incrementUsage(): void
    {
        $this->increment('usage_count');
    }



    /**
     * 作用域：按镜头类型筛选
     */
    public function scopeByCameraShot($query, string $cameraShot)
    {
        return $query->where('camera_shot', $cameraShot);
    }

    /**
     * 作用域：按难度等级筛选
     */
    public function scopeByDifficulty($query, string $difficulty)
    {
        return $query->where('difficulty_level', $difficulty);
    }

    /**
     * 作用域：用户上传的动作
     */
    public function scopeUserUploaded($query)
    {
        return $query->where('is_user_uploaded', true);
    }

    /**
     * 作用域：系统预设动作
     */
    public function scopeSystemPreset($query)
    {
        return $query->where('is_user_uploaded', false);
    }

    /**
     * 作用域：按使用次数排序
     */
    public function scopeOrderByUsage($query, string $direction = 'desc')
    {
        return $query->orderBy('usage_count', $direction);
    }

    /**
     * 作用域：按排序权重排序
     */
    public function scopeOrderBySortOrder($query, string $direction = 'asc')
    {
        return $query->orderBy('sort_order', $direction);
    }
}
