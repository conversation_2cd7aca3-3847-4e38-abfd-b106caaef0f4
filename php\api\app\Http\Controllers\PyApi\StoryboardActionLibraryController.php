<?php

namespace App\Http\Controllers\PyApi;

use App\Enums\ApiCodeEnum;
use App\Services\PyApi\StoryboardActionLibraryService;
use App\Services\AuthService;
use App\Http\Controllers\Controller;
use App\Helpers\LogCheckHelper;
use Illuminate\Support\Facades\Log;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;

/**
 * 分镜动作库控制器
 * 
 * 🎬 API接口功能：
 * ✅ 动作库列表查询（支持筛选和分页）
 * ✅ 动作详情查看
 * ✅ 动作创建（支持用户上传）
 * ✅ 动作更新和删除
 * ✅ 动作使用统计
 * ✅ 动作搜索功能
 */
class StoryboardActionLibraryController extends Controller
{
    protected StoryboardActionLibraryService $actionService;

    public function __construct(StoryboardActionLibraryService $actionService)
    {
        $this->actionService = $actionService;
    }

    /**
     * @ApiTitle (获取分镜动作库列表)
     * @ApiSummary (获取分镜动作库列表：需要Token认证)
     * @ApiMethod (GET)
     * @ApiRoute (/py-api/storyboard-actions)
     * @ApiParams (name="storyboard_id", type="integer", required=false, description="分镜ID，用于智能推荐排序")
     * @ApiParams (name="camera_shot", type="string", required=false, description="镜头类型筛选")
     * @ApiParams (name="difficulty_level", type="string", required=false, description="难度等级筛选")
     * @ApiParams (name="is_user_uploaded", type="boolean", required=false, description="是否用户上传")
     * @ApiParams (name="page", type="integer", required=false, description="页码")
     * @ApiParams (name="per_page", type="integer", required=false, description="每页数量")
     * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="数据")
     * @ApiReturn ({
     *   "code": 200,
     *   "message": "success",
     *   "data": {"data": [], "pagination": {}}
     * })
     */
    public function index(Request $request)
    {
        try {
            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
            }

            // 获取用户信息
            $user = $authResult['user'];

            $filters = $request->only([
                'camera_shot',
                'difficulty_level',
                'is_user_uploaded',
                'uploaded_by'
            ]);

            $perPage = $request->get('per_page', 15);
            $storyboardId = $request->get('storyboard_id');

            // 调用服务层处理业务
            $result = $this->actionService->getActionList($filters, $perPage, $storyboardId);

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                // 处理成功
                return $this->successResponse($result['data'], $result['message']);
            } else {
                // 处理失败
                return $this->errorResponse($result['code'], $result['message'], $result['data']);
            }

        } catch (\Exception $e) {
            Log::error('动作库列表获取失败', [
                'method' => __METHOD__,
                'user_id' => $user->id ?? null,
                'error_context' => LogCheckHelper::sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '动作库列表获取失败', null);
        }
    }

    /**
     * @ApiTitle (获取分镜动作详情)
     * @ApiSummary (获取分镜动作详情：需要Token认证)
     * @ApiMethod (GET)
     * @ApiRoute (/py-api/storyboard-actions/{id})
     * @ApiParams (name="id", type="integer", required=true, description="动作ID")
     * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="动作详情")
     * @ApiReturn ({
     *   "code": 200,
     *   "message": "success",
     *   "data": {}
     * })
     */
    public function show(Request $request, int $id)
    {
        try {
            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
            }

            // 获取用户信息
            $user = $authResult['user'];

            // 调用服务层处理业务
            $result = $this->actionService->getActionById($id);

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                // 处理成功
                return $this->successResponse($result['data'], $result['message']);
            } else {
                // 处理失败
                return $this->errorResponse($result['code'], $result['message'], $result['data']);
            }

        } catch (\Exception $e) {
            Log::error('动作详情获取失败', [
                'method' => __METHOD__,
                'user_id' => $user->id ?? null,
                'error_context' => LogCheckHelper::sanitize_request_for_log(['id' => $id]),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '动作详情获取失败', null);
        }
    }

    /**
     * @ApiTitle (创建分镜动作)
     * @ApiSummary (创建分镜动作：需要Token认证)
     * @ApiMethod (POST)
     * @ApiRoute (/py-api/storyboard-actions)
     * @ApiParams (name="title", type="string", required=true, description="动作标题")
     * @ApiParams (name="description", type="string", required=false, description="动作描述")
     * @ApiParams (name="camera_shot", type="string", required=true, description="镜头类型")
     * @ApiParams (name="action_type", type="string", required=true, description="动作类型")
     * @ApiParams (name="difficulty_level", type="string", required=false, description="难度等级")
     * @ApiParams (name="sketch_image_url", type="string", required=false, description="简笔画动作图URL")
     * @ApiParams (name="tags", type="array", required=false, description="动作标签")
     * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="创建的动作信息")
     * @ApiReturn ({
     *   "code": 200,
     *   "message": "success",
     *   "data": {}
     * })
     */
    public function store(Request $request)
    {
        try {
            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
            }

            // 获取用户信息
            $user = $authResult['user'];

            $validated = $request->validate([
                'title' => 'required|string|max:255',
                'description' => 'nullable|string',
                'camera_shot' => [
                    'required',
                    Rule::in([
                        'close_up', 'medium_shot', 'long_shot', 'extreme_close_up',
                        'extreme_long_shot', 'over_shoulder', 'bird_eye', 'worm_eye'
                    ])
                ],
                'action_type' => 'required|string|max:100',
                'difficulty_level' => 'nullable|in:easy,medium,hard',
                'sketch_image_url' => 'nullable|url|max:500',
                'tags' => 'nullable|array',
                'tags.*' => 'string|max:50'
            ]);

            // 设置上传用户信息
            $validated['is_user_uploaded'] = true;
            $validated['uploaded_by'] = $user->id;

            // 调用服务层处理业务
            $result = $this->actionService->createAction($validated);

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                // 处理成功
                return $this->successResponse($result['data'], $result['message']);
            } else {
                // 处理失败
                return $this->errorResponse($result['code'], $result['message'], $result['data']);
            }

        } catch (\Illuminate\Validation\ValidationException $e) {
            return $this->errorResponse(ApiCodeEnum::VALIDATION_ERROR, '参数验证失败', $e->errors());
        } catch (\Exception $e) {
            Log::error('动作创建失败', [
                'method' => __METHOD__,
                'user_id' => $user->id ?? null,
                'error_context' => LogCheckHelper::sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '动作创建失败', null);
        }
    }

    /**
     * @ApiTitle (更新分镜动作)
     * @ApiSummary (更新分镜动作：需要Token认证)
     * @ApiMethod (PUT)
     * @ApiRoute (/py-api/storyboard-actions/{id})
     * @ApiParams (name="id", type="integer", required=true, description="动作ID")
     * @ApiParams (name="title", type="string", required=false, description="动作标题")
     * @ApiParams (name="description", type="string", required=false, description="动作描述")
     * @ApiParams (name="camera_shot", type="string", required=false, description="镜头类型")
     * @ApiParams (name="action_type", type="string", required=false, description="动作类型")
     * @ApiParams (name="difficulty_level", type="string", required=false, description="难度等级")
     * @ApiParams (name="sketch_image_url", type="string", required=false, description="简笔画动作图URL")
     * @ApiParams (name="tags", type="array", required=false, description="动作标签")
     * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="boolean", required=true, description="更新结果")
     * @ApiReturn ({
     *   "code": 200,
     *   "message": "success",
     *   "data": true
     * })
     */
    public function update(Request $request, int $id)
    {
        try {
            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
            }

            // 获取用户信息
            $user = $authResult['user'];

            $validated = $request->validate([
                'title' => 'sometimes|string|max:255',
                'description' => 'nullable|string',
                'camera_shot' => [
                    'sometimes',
                    Rule::in([
                        'close_up', 'medium_shot', 'long_shot', 'extreme_close_up',
                        'extreme_long_shot', 'over_shoulder', 'bird_eye', 'worm_eye'
                    ])
                ],
                'action_type' => 'sometimes|string|max:100',
                'difficulty_level' => 'sometimes|in:easy,medium,hard',
                'sketch_image_url' => 'nullable|url|max:500',
                'tags' => 'nullable|array',
                'tags.*' => 'string|max:50'
            ]);

            // 调用服务层处理业务
            $result = $this->actionService->updateAction($id, $validated);

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                // 处理成功
                return $this->successResponse($result['data'], $result['message']);
            } else {
                // 处理失败
                return $this->errorResponse($result['code'], $result['message'], $result['data']);
            }

        } catch (\Illuminate\Validation\ValidationException $e) {
            return $this->errorResponse(ApiCodeEnum::VALIDATION_ERROR, '参数验证失败', $e->errors());
        } catch (\Exception $e) {
            Log::error('动作更新失败', [
                'method' => __METHOD__,
                'user_id' => $user->id ?? null,
                'error_context' => LogCheckHelper::sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '动作更新失败', null);
        }
    }

    /**
     * @ApiTitle (删除分镜动作)
     * @ApiSummary (删除分镜动作：需要Token认证)
     * @ApiMethod (DELETE)
     * @ApiRoute (/py-api/storyboard-actions/{id})
     * @ApiParams (name="id", type="integer", required=true, description="动作ID")
     * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="boolean", required=true, description="删除结果")
     * @ApiReturn ({
     *   "code": 200,
     *   "message": "success",
     *   "data": true
     * })
     */
    public function destroy(Request $request, int $id)
    {
        try {
            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
            }

            // 获取用户信息
            $user = $authResult['user'];

            // 调用服务层处理业务
            $result = $this->actionService->deleteAction($id);

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                // 处理成功
                return $this->successResponse($result['data'], $result['message']);
            } else {
                // 处理失败
                return $this->errorResponse($result['code'], $result['message'], $result['data']);
            }

        } catch (\Exception $e) {
            Log::error('动作删除失败', [
                'method' => __METHOD__,
                'user_id' => $user->id ?? null,
                'error_context' => LogCheckHelper::sanitize_request_for_log(['id' => $id]),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '动作删除失败', null);
        }
    }

    /**
     * @ApiTitle (搜索分镜动作)
     * @ApiSummary (搜索分镜动作：需要Token认证)
     * @ApiMethod (GET)
     * @ApiRoute (/py-api/storyboard-actions/search)
     * @ApiParams (name="keyword", type="string", required=true, description="搜索关键词")
     * @ApiParams (name="camera_shot", type="string", required=false, description="镜头类型筛选")
     * @ApiParams (name="difficulty_level", type="string", required=false, description="难度等级筛选")
     * @ApiParams (name="page", type="integer", required=false, description="页码")
     * @ApiParams (name="per_page", type="integer", required=false, description="每页数量")
     * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="搜索结果")
     * @ApiReturn ({
     *   "code": 200,
     *   "message": "success",
     *   "data": {"data": [], "pagination": {}}
     * })
     */
    public function search(Request $request)
    {
        try {
            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
            }

            // 获取用户信息
            $user = $authResult['user'];

            $request->validate([
                'keyword' => 'required|string|min:1|max:100'
            ]);

            $keyword = $request->get('keyword');
            $filters = $request->only([
                'camera_shot',
                'difficulty_level'
            ]);
            $perPage = $request->get('per_page', 15);

            // 调用服务层处理业务
            $result = $this->actionService->searchActions($keyword, $filters, $perPage);

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                // 处理成功
                return $this->successResponse($result['data'], $result['message']);
            } else {
                // 处理失败
                return $this->errorResponse($result['code'], $result['message'], $result['data']);
            }

        } catch (\Illuminate\Validation\ValidationException $e) {
            return $this->errorResponse(ApiCodeEnum::VALIDATION_ERROR, '参数验证失败', $e->errors());
        } catch (\Exception $e) {
            Log::error('动作搜索失败', [
                'method' => __METHOD__,
                'user_id' => $user->id ?? null,
                'error_context' => LogCheckHelper::sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '动作搜索失败', null);
        }
    }
}
