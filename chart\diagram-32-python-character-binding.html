<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Py视频创作工具业务流程D-2: 绑定角色流程（标准化版）</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .mermaid {
            text-align: center;
        }
        .back-button {
            position: fixed;
            top: 20px;
            left: 20px;
            background: rgba(255,255,255,0.9);
            border: none;
            padding: 10px 20px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 14px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }
        .back-button:hover {
            background: white;
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }
        .description {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            border-left: 4px solid #007bff;
        }
        .features {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 5px;
            margin-top: 20px;
        }
        .features h3 {
            color: #333;
            margin-bottom: 15px;
        }
        .features ul {
            list-style-type: none;
            padding-left: 0;
        }
        .features li {
            margin-bottom: 8px;
            padding-left: 20px;
            position: relative;
        }
        .features li:before {
            content: "✓";
            position: absolute;
            left: 0;
            color: #28a745;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <button class="back-button" onclick="window.location.href='all-diagrams-index.html'">← 返回图表索引</button>
    
    <div class="container">
        <h1>🔗 Py视频创作工具业务流程D-2: 绑定角色流程（标准化版）</h1>
        
        <div class="description">
            <strong>流程说明：</strong>本图表展示了Py视频创作工具中"角色绑定"功能的标准化业务流程，用户从项目创建页面跳转到角色绑定页面后，系统会自动查询项目中提取的角色信息，动态生成对应数量的角色坑位。用户可以选择"一键选形象"为所有角色批量匹配形象，或单独为每个角色选择形象。支持从角色库中选择现有角色或新增角色进行绑定。<strong>重要说明：</strong>选择现有角色绑定不消费积分；新增角色时，积分消费在角色创建流程（diagram-character-creation-shared.html）中完成，创建成功后直接进行绑定，无需额外积分消费。引用D-1图表进行了全面优化，包括错误处理、资源管理等机制，确保与系统其他流程的规范一致性。
        </div>

        <div class="mermaid">
sequenceDiagram
    participant U as 用户
    participant F as Py视频创作工具前端
    participant W as WebSocket服务
    participant A as 工具API接口服务
    participant SC as AiServiceClient
    participant RM as AI资源管理服务
    participant AI as AI平台
    participant DB as MySQL数据库
    participant R as Redis缓存
    participant E as 事件总线
    participant PG as 剧情图片生成界面

    Note over U: 用户从项目创建页面跳转到角色绑定页面

    Note over F: 🔐 Token验证流程
    Note over F: 引用 diagram-22-python-token-validation.html
    F->>A: 提交角色绑定页面请求(包含Token和project_id)
    A->>A: 验证Token和项目参数
    A->>DB: 查询用户状态和项目权限

    Note over F: 🎭 角色绑定页面初始化流程
    F->>A: GET /py-api/projects/{id}/characters
    A->>DB: 查询项目的角色信息(project_characters表)
    A->>R: 检查角色缓存
    A->>F: 返回项目角色列表(动态数量)

    F->>F: 根据角色数量动态生成坑位界面
    F->>U: 显示角色绑定界面(动态N个角色坑位)

    loop 每个提取的角色
        F->>U: 显示角色坑位(角色名称+描述+选择按钮)
    end

    Note over U: 用户可以选择"一键选形象"或单独为角色选择形象
    alt 用户选择"一键选形象"
        U->>F: 点击"一键选形象"按钮
        Note over F: 为所有角色批量智能匹配形象
    else 用户单独选择角色形象
        U->>F: 点击某个角色坑位
        F->>U: 弹出角色选择对话框
    end

    Note over F: 📋 角色库查询流程
    F->>A: GET /py-api/characters/library
    A->>DB: 查询可用角色库
    A->>R: 检查角色库缓存
    A->>F: 返回角色列表
    F->>U: 显示角色选择界面(小框点击弹出角色列表)

    alt 用户选择现有角色
        U->>F: 选择现有角色进行绑定
        F->>A: 提交角色绑定请求(角色ID+分镜位置ID)
    else 用户选择新增角色
        U->>F: 点击"新增角色"按钮
        F->>F: 调用角色创建罩层<br/>PyVideoTool.CharacterCreationModal.open({<br/>mode: 'filtered', filters: {projectType, style},<br/>callbacks: {onSuccess: bindNewCharacter}})
        Note over F: 引用 diagram-character-creation-shared.html
        Note over F: 角色创建罩层处理完整创建流程
        F->>F: 接收角色创建成功回调
        F->>A: 提交新角色绑定请求(新角色ID+分镜位置ID)
    end

    Note over A: 🎯 执行角色绑定操作
    Note over A: 角色已创建完成，无需积分消费和平台选择
    A->>A: POST /py-api/characters/bind<br/>character_id={new_character_id}, storyboard_position_id={position_id}
    A->>DB: 创建角色分镜绑定记录
    A->>R: 更新绑定缓存
    DB->>A: 返回绑定配置ID和扩展信息
    A->>RM: 更新分镜资源信息

    alt 角色绑定失败
        A->>DB: 更新业务日志(状态:失败)
        A->>E: 发布失败事件(异步处理)
        A->>F: 返回失败结果
        F->>U: 显示角色绑定失败提示
        Note over A: 绑定失败，无积分操作
    else 角色绑定成功
        A->>DB: 更新业务日志(状态:成功)
        A->>F: 返回绑定配置数据
        F->>U: 显示绑定成功的角色(更新分镜中的角色显示)
        Note over A: 绑定成功，无积分操作

        Note over A: 📊 用户偏好学习与优化
        A->>DB: 记录用户平台选择行为<br/>(智能推荐/选择理由/角色绑定任务/绑定质量)
        A->>DB: 更新用户偏好权重<br/>(质量优先/速度优先/兼容性优先)
        A->>R: 更新用户常用平台缓存
        A->>R: 刷新推荐算法缓存<br/>为下次绑定优化准备数据
    end

    Note over F: 🎯 后续操作选择
    alt 用户继续编辑分镜
        F->>U: 显示更新后的分镜编辑界面
        Note over U: 用户可以继续绑定其他角色或编辑分镜
    else 用户进入生图模式
        U->>F: 点击"进入生图"按钮
        F->>F: 跳转到生图页面
        F->>PG: 传递角色绑定数据，启动剧情图片生成流程
    end

    Note over U: 角色绑定流程完成，可继续分镜编辑或进入生图模式
        </div>

        <div class="features">
            <h3>🎯 图表中体现的标准化流程特性：</h3>
            <ul>
                <li><strong>🎭 动态角色坑位生成：</strong>根据项目中提取的角色数量动态生成对应的角色坑位界面</li>
                <li><strong>📊 项目角色信息查询：</strong>从project_characters表查询项目的角色信息，支持动态数量的角色显示</li>
                <li><strong>⚡ 一键选形象功能：</strong>支持为所有角色批量智能匹配形象，提升用户操作效率</li>
                <li><strong>🔗 角色绑定处理：</strong>支持从角色库中选择角色并绑定到角色坑位，包含兼容性验证和绑定操作</li>
                <li><strong>📋 角色选择对话框：</strong>点击角色坑位弹出角色选择界面，支持选择现有角色或新增角色</li>
                <li><strong>🆕 新增角色罩层集成：</strong>支持在绑定流程中调用统一的角色创建罩层，创建完成后通过回调自动绑定新角色</li>
                <li><strong>🔄 统一罩层调用：</strong>引用diagram-character-creation-shared.html的标准化角色创建流程，确保UI和业务逻辑的一致性</li>
                <li><strong>📋 参数化罩层配置：</strong>根据绑定场景自动配置罩层参数（filtered模式，传递项目类型和风格筛选条件）</li>
                <li><strong>📋 角色库查询：</strong>支持查询可用角色库，提供角色选择界面，包含缓存优化</li>
                <li><strong>🔒 无积分消费设计：</strong>选择现有角色绑定不消费积分；新增角色的积分消费在角色创建流程中完成</li>
                <li><strong>⚠️ 错误处理机制：</strong>完善的绑定失败处理和用户提示机制</li>
                <li><strong>📦 资源管理标准化：</strong>创建和更新分镜资源记录，维护绑定关系</li>
                <li><strong>🔐 标准化Token验证：</strong>复用diagram-22-python-token-validation.html标准Token验证流程</li>
                <li><strong>💾 数据库操作：</strong>创建角色分镜绑定记录，缓存绑定配置到Redis</li>
                <li><strong>🎭 分镜兼容性验证：</strong>验证角色与分镜位置的兼容性，确保绑定的有效性</li>
                <li><strong>🎮 后续操作支持：</strong>支持继续编辑分镜或进入生图模式，提供灵活的工作流程</li>
                <li><strong>⚡ 缓存优化：</strong>角色库和分镜信息缓存，提升查询性能</li>
            </ul>

            <h3>🔧 API接口调用示例（2025-08-07 角色坑位版）：</h3>
            <div style="background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin-bottom: 20px;">
                <pre style="background-color: #e9ecef; padding: 10px; border-radius: 3px; overflow-x: auto;">
// 1. 获取项目角色信息（页面初始化）
GET /py-api/projects/{id}/characters
{
    "code": 200,
    "message": "角色信息获取成功",
    "data": {
        "project_id": 123,
        "characters": [
            {
                "id": 1,
                "name": "主角",
                "description": "勇敢的年轻人",
                "role_type": "protagonist",
                "importance": "high",
                "binding_status": "unbound"
            },
            {
                "id": 2,
                "name": "配角",
                "description": "忠实的朋友",
                "role_type": "supporting",
                "importance": "medium",
                "binding_status": "unbound"
            }
        ]
    }
}

// 2. 一键选形象接口
POST /py-api/projects/{id}/auto-select-characters
{
    "mode": "intelligent_match",
    "preferences": {
        "style": "realistic",
        "quality_priority": "high"
    }
}

// 3. 单个角色绑定接口
POST /py-api/characters/bind
{
    "character_id": 123,                        // 角色ID
    "reason": "分镜场景绑定",                   // 绑定原因
    "storyboard_position_id": "pos_scene_01",  // 新增：分镜位置ID
    "binding_context": "storyboard",           // 新增：绑定上下文
    "auto_bind": true,                         // 新增：自动绑定标识
    "compatibility_check": true                // 新增：兼容性检查
}

// 响应示例（扩展版）
{
    "code": 200,
    "message": "角色绑定成功",
    "data": {
        "binding_id": 456,
        "character_id": 123,
        "character_name": "勇敢的骑士",
        "storyboard_position_id": "pos_scene_01",  // 新增：分镜位置信息
        "binding_context": "storyboard",           // 新增：绑定上下文
        "compatibility_result": {                  // 新增：兼容性检查结果
            "compatible": true,
            "score": 0.95,
            "suggestions": []
        },
        "created_at": "2025-08-07 12:00:00"
    }
}

// 如果需要先创建新角色再绑定，可以使用：
POST /py-api/characters/create-from-file
{
    "file_id": "file_789",
    "style": "写实风3.0",
    "project_id": 123,
    "auto_bind": true,                         // 创建后自动绑定
    "storyboard_position_id": "pos_scene_01"  // 绑定到指定分镜位置
}</pre>
            </div>

            <h3>📚 C系列图表引用说明：</h3>
            <p>图表中的引用标注说明：</p>
            <ul>
                <li><strong>C-1：</strong>AI任务调度 - 智能平台选择机制</li>
                <li><strong>C-2：</strong>AI生成成功 - 积分确认扣取流程</li>
                <li><strong>C-3：</strong>积分不足 - 快速验证机制</li>
                <li><strong>C-4：</strong>AI生成失败 - 事件总线异步处理</li>
                <li><strong>C-6：</strong>资源管理 - AI资源管理服务</li>
                <li><strong>C-9：</strong>环境切换 - AiServiceClient统一调用</li>
            </ul>
            <p><em>这些引用标注帮助开发者理解当前流程与标准化规范的对应关系，确保实现的一致性。</em></p>
        </div>
    </div>

    <script>
        mermaid.initialize({
            startOnLoad: true,
            theme: 'default',
            flowchart: {
                useMaxWidth: true,
                htmlLabels: true
            }
        });
    </script>
</body>
</html>
