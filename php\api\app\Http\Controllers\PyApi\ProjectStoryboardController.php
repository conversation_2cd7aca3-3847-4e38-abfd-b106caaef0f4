<?php

namespace App\Http\Controllers\PyApi;

use App\Http\Controllers\Controller;
use App\Services\PyApi\ProjectStoryboardService;
use App\Services\AuthService;
use App\Enums\ApiCodeEnum;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use App\Helpers\LogCheckHelper;

/**
 * 项目分镜管理控制器
 * 提供分镜的CRUD操作接口
 */
class ProjectStoryboardController extends Controller
{
    protected $storyboardService;

    public function __construct(ProjectStoryboardService $storyboardService)
    {
        $this->storyboardService = $storyboardService;
    }

    /**
     * 获取项目分镜列表
     * @ApiMethod (GET)
     * @ApiRoute (/py-api/projects/{project_id}/storyboards)
     * @ApiParams (name="project_id", type="int", required=true, description="项目ID")
     * @ApiParams (name="status", type="string", required=false, description="分镜状态筛选")
     * @ApiParams (name="page", type="int", required=false, description="页码，默认1")
     * @ApiParams (name="per_page", type="int", required=false, description="每页数量，默认10")
     */
    public function index($projectId, Request $request)
    {
        try {
            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
            }

            $user = $authResult['user'];
            
            // 验证项目权限
            $projectCheck = $this->storyboardService->checkProjectAccess($user->id, $projectId);
            if ($projectCheck['code'] !== ApiCodeEnum::SUCCESS) {
                return $this->errorResponse($projectCheck['code'], $projectCheck['message']);
            }

            $filters = [
                'status' => $request->get('status'),
            ];
            
            $page = (int) $request->get('page', 1);
            $perPage = (int) $request->get('per_page', 10);

            $result = $this->storyboardService->getProjectStoryboards($projectId, $filters, $page, $perPage);

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                return $this->successResponse($result['data'], $result['message']);
            } else {
                return $this->errorResponse($result['code'], $result['message'], $result['data']);
            }

        } catch (\Exception $e) {
            Log::error('获取项目分镜列表失败', [
                'method' => __METHOD__,
                'project_id' => $projectId,
                'user_id' => $user->id ?? null,
                'error_context' => LogCheckHelper::sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '获取分镜列表失败', []);
        }
    }

    /**
     * 获取分镜详情
     * @ApiMethod (GET)
     * @ApiRoute (/py-api/storyboards/{id})
     * @ApiParams (name="id", type="int", required=true, description="分镜ID")
     */
    public function show($id, Request $request)
    {
        try {
            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
            }

            $user = $authResult['user'];

            $result = $this->storyboardService->getStoryboardDetail($id, $user->id);

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                return $this->successResponse($result['data'], $result['message']);
            } else {
                return $this->errorResponse($result['code'], $result['message'], $result['data']);
            }

        } catch (\Exception $e) {
            Log::error('获取分镜详情失败', [
                'method' => __METHOD__,
                'storyboard_id' => $id,
                'user_id' => $user->id ?? null,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '获取分镜详情失败', []);
        }
    }

    /**
     * 创建分镜
     * @ApiMethod (POST)
     * @ApiRoute (/py-api/projects/{project_id}/storyboards)
     * @ApiParams (name="project_id", type="int", required=true, description="项目ID")
     * @ApiParams (name="scene_title", type="string", required=true, description="分镜标题")
     * @ApiParams (name="scene_description", type="string", required=true, description="分镜描述")
     * @ApiParams (name="scene_number", type="int", required=false, description="分镜序号，不传则自动生成")
     * @ApiParams (name="camera_angle", type="string", required=false, description="镜头角度")
     * @ApiParams (name="shot_type", type="string", required=false, description="镜头类型")
     * @ApiParams (name="duration", type="int", required=false, description="预计时长（秒）")
     */
    public function store($projectId, Request $request)
    {
        try {
            $rules = [
                'scene_title' => 'required|string|max:200',
                'scene_description' => 'required|string|max:2000',
                'scene_number' => 'sometimes|integer|min:1',
                'camera_angle' => 'sometimes|string|max:50',
                'shot_type' => 'sometimes|string|max:50',
                'lighting' => 'sometimes|string|max:100',
                'background_description' => 'sometimes|string|max:1000',
                'duration' => 'sometimes|integer|min:1|max:300'
            ];

            $messages = [
                'scene_title.required' => '分镜标题不能为空',
                'scene_title.max' => '分镜标题不能超过200个字符',
                'scene_description.required' => '分镜描述不能为空',
                'scene_description.max' => '分镜描述不能超过2000个字符',
                'scene_number.min' => '分镜序号必须大于0',
                'duration.min' => '时长必须大于0秒',
                'duration.max' => '时长不能超过300秒'
            ];

            $this->validateData($request->all(), $rules, $messages, []);

            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
            }

            $user = $authResult['user'];

            $storyboardData = [
                'scene_title' => $request->scene_title,
                'scene_description' => $request->scene_description,
                'scene_number' => $request->scene_number,
                'camera_angle' => $request->camera_angle,
                'shot_type' => $request->shot_type,
                'lighting' => $request->lighting,
                'background_description' => $request->background_description,
                'duration' => $request->duration
            ];

            $result = $this->storyboardService->createStoryboard($user->id, $projectId, $storyboardData);

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                return $this->successResponse($result['data'], $result['message']);
            } else {
                return $this->errorResponse($result['code'], $result['message'], $result['data']);
            }

        } catch (\Exception $e) {
            Log::error('创建分镜失败', [
                'method' => __METHOD__,
                'project_id' => $projectId,
                'user_id' => $user->id ?? null,
                'error_context' => LogCheckHelper::sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '创建分镜失败', []);
        }
    }

    /**
     * 更新分镜
     * @ApiMethod (PUT)
     * @ApiRoute (/py-api/storyboards/{id})
     * @ApiParams (name="id", type="int", required=true, description="分镜ID")
     */
    public function update($id, Request $request)
    {
        try {
            $rules = [
                'scene_title' => 'sometimes|string|max:200',
                'scene_description' => 'sometimes|string|max:2000',
                'scene_number' => 'sometimes|integer|min:1',
                'camera_angle' => 'sometimes|string|max:50',
                'shot_type' => 'sometimes|string|max:50',
                'lighting' => 'sometimes|string|max:100',
                'background_description' => 'sometimes|string|max:1000',
                'duration' => 'sometimes|integer|min:1|max:300',
                'status' => 'sometimes|in:draft,approved,generating,completed,failed',
                'ai_prompt' => 'sometimes|string|max:4000'
            ];

            $this->validateData($request->all(), $rules, [], []);

            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
            }

            $user = $authResult['user'];

            $updateData = $request->only([
                'scene_title', 'scene_description', 'scene_number', 'camera_angle',
                'shot_type', 'lighting', 'background_description', 'duration', 'status', 'ai_prompt'
            ]);

            $result = $this->storyboardService->updateStoryboard($id, $user->id, $updateData);

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                return $this->successResponse($result['data'], $result['message']);
            } else {
                return $this->errorResponse($result['code'], $result['message'], $result['data']);
            }

        } catch (\Exception $e) {
            Log::error('更新分镜失败', [
                'method' => __METHOD__,
                'storyboard_id' => $id,
                'user_id' => $user->id ?? null,
                'error_context' => LogCheckHelper::sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '更新分镜失败', []);
        }
    }

    /**
     * 删除分镜
     * @ApiMethod (DELETE)
     * @ApiRoute (/py-api/storyboards/{id})
     * @ApiParams (name="id", type="int", required=true, description="分镜ID")
     */
    public function destroy($id, Request $request)
    {
        try {
            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
            }

            $user = $authResult['user'];

            $result = $this->storyboardService->deleteStoryboard($id, $user->id);

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                return $this->successResponse($result['data'], $result['message']);
            } else {
                return $this->errorResponse($result['code'], $result['message'], $result['data']);
            }

        } catch (\Exception $e) {
            Log::error('删除分镜失败', [
                'method' => __METHOD__,
                'storyboard_id' => $id,
                'user_id' => $user->id ?? null,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '删除分镜失败', []);
        }
    }

    /**
     * 批量更新分镜排序
     * @ApiMethod (PUT)
     * @ApiRoute (/py-api/projects/{project_id}/storyboards/reorder)
     * @ApiParams (name="project_id", type="int", required=true, description="项目ID")
     * @ApiParams (name="storyboards", type="array", required=true, description="分镜排序数据")
     */
    public function reorder($projectId, Request $request)
    {
        try {
            $rules = [
                'storyboards' => 'required|array|min:1',
                'storyboards.*.id' => 'required|integer',
                'storyboards.*.scene_number' => 'required|integer|min:1'
            ];

            $messages = [
                'storyboards.required' => '分镜排序数据不能为空',
                'storyboards.array' => '分镜排序数据格式错误',
                'storyboards.*.id.required' => '分镜ID不能为空',
                'storyboards.*.scene_number.required' => '分镜序号不能为空'
            ];

            $this->validateData($request->all(), $rules, $messages, []);

            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
            }

            $user = $authResult['user'];

            $result = $this->storyboardService->reorderStoryboards($user->id, $projectId, $request->storyboards);

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                return $this->successResponse($result['data'], $result['message']);
            } else {
                return $this->errorResponse($result['code'], $result['message'], $result['data']);
            }

        } catch (\Exception $e) {
            Log::error('分镜排序失败', [
                'method' => __METHOD__,
                'project_id' => $projectId,
                'user_id' => $user->id ?? null,
                'error_context' => LogCheckHelper::sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '分镜排序失败', []);
        }
    }

    /**
     * 批量生成分镜
     * @ApiMethod (POST)
     * @ApiRoute (/py-api/projects/{project_id}/storyboards/batch-generate)
     * @ApiParams (name="project_id", type="int", required=true, description="项目ID")
     * @ApiParams (name="storyboard_ids", type="array", required=false, description="指定分镜ID列表，不传则生成所有未生成的分镜")
     * @ApiParams (name="generation_params", type="object", required=false, description="生成参数")
     */
    public function batchGenerate($projectId, Request $request)
    {
        try {
            $rules = [
                'storyboard_ids' => 'sometimes|array',
                'storyboard_ids.*' => 'integer',
                'generation_params' => 'sometimes|array'
            ];

            $this->validateData($request->all(), $rules, [], []);

            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
            }

            $user = $authResult['user'];

            $storyboardIds = $request->get('storyboard_ids', []);
            $generationParams = $request->get('generation_params', []);

            $result = $this->storyboardService->batchGenerateStoryboards($user->id, $projectId, $storyboardIds, $generationParams);

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                return $this->successResponse($result['data'], $result['message']);
            } else {
                return $this->errorResponse($result['code'], $result['message'], $result['data']);
            }

        } catch (\Exception $e) {
            Log::error('批量生成分镜失败', [
                'method' => __METHOD__,
                'project_id' => $projectId,
                'user_id' => $user->id ?? null,
                'error_context' => LogCheckHelper::sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '批量生成分镜失败', []);
        }
    }
}
