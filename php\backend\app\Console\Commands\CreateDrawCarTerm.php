<?php

namespace App\Console\Commands;

use App\Models\DrawCar;
use App\Models\DrawCarTerm;
use Illuminate\Console\Command;
use App\Services\PyApi\Config\SystemConfig;
use Illuminate\Support\Facades\Cache;

class CreateDrawCarTerm extends Command
{
    /**
     * 名称
     * 执行命令 : php artisan create_draw_car_term
     * @var string
     */
    protected $signature = 'create_draw_car_term';

    /**
     * 描述
     * @var string
     */
    protected $description = '创建座驾抽奖期次';

    /**
     * RefundUserWindow constructor.
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * 执行的业务
     */
    public function handle()
    {
        $starttime = time();

        $term = DrawCarTerm::query()->select('key', 'end_at')->orderByDesc('id')->first();
        if(empty($term) || ($term->end_at - time()) < 24 * 3600)
        {
            $key = 1;
            if(!empty($term))
            {
                $draw = DrawCar::query()->select('key')
                    ->where('key','>',$term->key)
                    ->orderBy('key')
                    ->first();
                if(!empty($draw))
                {
                    $key = $draw->key;
                }
            }
            $end_at = time() + SystemConfig::getRow('drawcar_time') * 24 * 3600;
            $data = [
                'key' => $key,
                'end_at' => $end_at
            ];
            DrawCarTerm::query()->create($data);
            $cache_key = 'db:draw_car_terms:row';
            Cache::set($cache_key, $data);
        }

        $timelength = time() - $starttime;
        $minute = floor($timelength/60);
        $second = $timelength%60;
        echo '执行结束,用时：'.$minute.'分钟'.$second.'秒'.PHP_EOL;
    }
}

