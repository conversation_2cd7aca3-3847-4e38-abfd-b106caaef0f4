<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Carbon\Carbon;

/**
 * 项目分镜模型
 * 
 * @property int $id
 * @property int $project_id
 * @property int $scene_number
 * @property string $scene_title
 * @property string $scene_description
 * @property string $camera_angle
 * @property string $shot_type
 * @property string $lighting
 * @property string $background_description
 * @property string $ai_prompt
 * @property array $generation_params
 * @property array $style_overrides
 * @property int $duration
 * @property string $status
 * @property int $generated_image_id
 * @property int $generated_video_id
 * @property array $metadata
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 */
class ProjectStoryboard extends Model
{
    /**
     * 表名（框架会自动添加p_前缀）
     */
    protected $table = 'project_storyboards';

    /**
     * 可批量赋值的属性
     */
    protected $fillable = [
        'project_id',
        'scene_number',
        'scene_title',
        'scene_description',
        'camera_angle',
        'shot_type',
        'lighting',
        'background_description',
        'ai_prompt',
        'generation_params',
        'style_overrides',
        'duration',
        'status',
        'generated_image_id',
        'generated_video_id',
        'action_library_id',
        'metadata'
    ];

    /**
     * 属性类型转换
     */
    protected $casts = [
        'generation_params' => 'array',
        'style_overrides' => 'array',
        'metadata' => 'array',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];

    /**
     * 默认值
     */
    protected $attributes = [
        'status' => 'draft'
    ];

    /**
     * 状态常量
     */
    const STATUS_DRAFT = 'draft';
    const STATUS_APPROVED = 'approved';
    const STATUS_GENERATING = 'generating';
    const STATUS_COMPLETED = 'completed';
    const STATUS_FAILED = 'failed';

    /**
     * 关联项目
     */
    public function project(): BelongsTo
    {
        return $this->belongsTo(Project::class);
    }

    /**
     * 关联分镜角色
     */
    public function characters(): HasMany
    {
        return $this->hasMany(StoryboardCharacter::class, 'storyboard_id');
    }

    /**
     * 关联分镜旁白/解说
     */
    public function narration(): HasOne
    {
        return $this->hasOne(StoryboardNarration::class, 'storyboard_id');
    }

    /**
     * 关联生成的图片资源
     */
    public function generatedImage(): BelongsTo
    {
        return $this->belongsTo(Resource::class, 'generated_image_id');
    }

    /**
     * 关联生成的视频资源
     */
    public function generatedVideo(): BelongsTo
    {
        return $this->belongsTo(Resource::class, 'generated_video_id');
    }

    /**
     * 关联分镜动作库
     */
    public function actionLibrary(): BelongsTo
    {
        return $this->belongsTo(StoryboardActionLibrary::class, 'action_library_id');
    }

    /**
     * 查询作用域：按项目筛选
     */
    public function scopeByProject($query, int $projectId)
    {
        return $query->where('project_id', $projectId);
    }

    /**
     * 查询作用域：按状态筛选
     */
    public function scopeByStatus($query, string $status)
    {
        return $query->where('status', $status);
    }

    /**
     * 查询作用域：按场景号排序
     */
    public function scopeOrderByScene($query)
    {
        return $query->orderBy('scene_number');
    }

    /**
     * 标记为已批准
     */
    public function approve(): void
    {
        $this->status = self::STATUS_APPROVED;
        $this->save();
    }

    /**
     * 标记为生成中
     */
    public function markAsGenerating(): void
    {
        $this->status = self::STATUS_GENERATING;
        $this->save();
    }

    /**
     * 标记为已完成
     */
    public function markAsCompleted(): void
    {
        $this->status = self::STATUS_COMPLETED;
        $this->save();
    }

    /**
     * 标记为失败
     */
    public function markAsFailed(): void
    {
        $this->status = self::STATUS_FAILED;
        $this->save();
    }

    /**
     * 获取分镜配置
     */
    public function getGenerationParam(string $key, $default = null)
    {
        return data_get($this->generation_params, $key, $default);
    }

    /**
     * 设置分镜配置
     */
    public function setGenerationParam(string $key, $value): void
    {
        $params = $this->generation_params ?? [];
        data_set($params, $key, $value);
        $this->generation_params = $params;
        $this->save();
    }

    /**
     * 获取元数据
     */
    public function getMetadata(string $key, $default = null)
    {
        return data_get($this->metadata, $key, $default);
    }

    /**
     * 设置元数据
     */
    public function setMetadata(string $key, $value): void
    {
        $metadata = $this->metadata ?? [];
        data_set($metadata, $key, $value);
        $this->metadata = $metadata;
        $this->save();
    }
}
