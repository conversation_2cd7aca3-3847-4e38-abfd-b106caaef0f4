<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * 分镜旁白/解说模型
 * 
 * @property int $id
 * @property int $storyboard_id
 * @property int|null $narrator_character_id
 * @property string|null $voice_id
 * @property string $platform
 * @property string $language
 * @property string $emotion
 * @property float $speed
 * @property float $pitch
 * @property string|null $subtitle_text
 * @property string $status
 * @property string|null $last_preview_url
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 */
class StoryboardNarration extends Model
{
    /**
     * 表名（框架会自动添加p_前缀）
     */
    protected $table = 'storyboard_narrations';

    /**
     * 可批量赋值的属性
     */
    protected $fillable = [
        'storyboard_id',
        'narrator_character_id',
        'voice_id',
        'platform',
        'language',
        'emotion',
        'speed',
        'pitch',
        'subtitle_text',
        'status',
        'last_preview_url'
    ];

    /**
     * 属性类型转换
     */
    protected $casts = [
        'speed' => 'decimal:1',
        'pitch' => 'decimal:1',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];

    /**
     * 默认值
     */
    protected $attributes = [
        'platform' => 'volcengine',
        'language' => 'zh-CN',
        'emotion' => 'neutral',
        'speed' => 1.0,
        'pitch' => 1.0,
        'status' => 'active'
    ];

    /**
     * 状态常量
     */
    const STATUS_ACTIVE = 'active';
    const STATUS_INACTIVE = 'inactive';

    /**
     * 支持的语言
     */
    const SUPPORTED_LANGUAGES = ['zh-CN', 'en-US', 'ja-JP', 'ko-KR'];

    /**
     * 支持的情绪
     */
    const SUPPORTED_EMOTIONS = [
        'neutral', 'happy', 'sad', 'angry', 
        'excited', 'calm', 'energetic'
    ];

    /**
     * 关联分镜
     */
    public function storyboard(): BelongsTo
    {
        return $this->belongsTo(ProjectStoryboard::class, 'storyboard_id');
    }

    /**
     * 关联解说角色（项目角色）
     */
    public function narratorCharacter(): BelongsTo
    {
        return $this->belongsTo(ProjectCharacter::class, 'narrator_character_id');
    }

    /**
     * 查询作用域：按分镜筛选
     */
    public function scopeByStoryboard($query, int $storyboardId)
    {
        return $query->where('storyboard_id', $storyboardId);
    }

    /**
     * 查询作用域：按状态筛选
     */
    public function scopeByStatus($query, string $status)
    {
        return $query->where('status', $status);
    }

    /**
     * 查询作用域：活跃状态
     */
    public function scopeActive($query)
    {
        return $query->where('status', self::STATUS_ACTIVE);
    }

    /**
     * 检查是否已配置音色
     */
    public function hasVoiceConfigured(): bool
    {
        return !empty($this->voice_id);
    }

    /**
     * 检查是否已配置解说角色
     */
    public function hasNarratorConfigured(): bool
    {
        return !empty($this->narrator_character_id);
    }

    /**
     * 获取有效的旁白文本（优先使用自定义，否则取分镜默认）
     */
    public function getEffectiveSubtitleText(): ?string
    {
        if (!empty($this->subtitle_text)) {
            return $this->subtitle_text;
        }
        
        // 如果没有自定义旁白文本，从分镜获取默认文本
        // 注：这里需要根据实际分镜模型的字段名调整
        return $this->storyboard->scene_description ?? null;
    }

    /**
     * 转换为API响应格式
     */
    public function toApiArray(): array
    {
        return [
            'id' => $this->id,
            'storyboard_id' => $this->storyboard_id,
            'narrator_character_id' => $this->narrator_character_id,
            'voice_id' => $this->voice_id,
            'platform' => $this->platform,
            'language' => $this->language,
            'emotion' => $this->emotion,
            'speed' => $this->speed,
            'pitch' => $this->pitch,
            'subtitle_text' => $this->subtitle_text,
            'effective_subtitle_text' => $this->getEffectiveSubtitleText(),
            'status' => $this->status,
            'last_preview_url' => $this->last_preview_url,
            'has_voice_configured' => $this->hasVoiceConfigured(),
            'has_narrator_configured' => $this->hasNarratorConfigured(),
            'created_at' => $this->created_at?->toISOString(),
            'updated_at' => $this->updated_at?->toISOString(),
            'narrator_character' => $this->narratorCharacter?->toApiArray(),
        ];
    }
}
