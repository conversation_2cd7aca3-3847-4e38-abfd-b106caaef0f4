<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

/**
 * 创建分镜动作库表
 * 
 * 🎬 分镜动作库功能：
 * ✅ 支持镜头参数分类（特写/中景/远景等8种镜头类型）
 * ✅ 支持动作类型管理（躺着/睡觉/向上看/开门/坐下等）
 * ✅ 支持用户上传和系统预设动作
 * ✅ 支持简笔画动作图和参考视频
 * ✅ 支持动作分类、情绪状态、难度等级
 * ✅ 支持评分、使用统计、推荐排序
 */
return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('p_storyboard_action_library', function (Blueprint $table) {
            $table->id()->comment('动作库ID');
            
            // 基本信息
            $table->string('title', 255)->comment('动作标题');
            $table->text('description')->nullable()->comment('动作描述');
            
            // 镜头参数
            $table->enum('camera_shot', [
                'close_up',           // 特写
                'medium_shot',        // 中景
                'long_shot',          // 远景
                'extreme_close_up',   // 大特写
                'extreme_long_shot',  // 大远景
                'over_shoulder',      // 过肩镜头
                'bird_eye',           // 鸟瞰
                'worm_eye'            // 仰视
            ])->comment('镜头参数（特写/中景/远景/大特写/大远景/过肩镜头/鸟瞰/仰视）');
            
            // 动作信息
            $table->string('action_type', 100)->comment('动作类型（躺着/睡觉/向上看/开门/坐下/走路/跑步/跳跃/挥手/拥抱等）');
            $table->enum('difficulty_level', ['easy', 'medium', 'hard'])->default('medium')->comment('动作难度（简单/中等/困难）');

            // 资源文件
            $table->string('sketch_image_url', 500)->nullable()->comment('简笔画动作图URL');
            
            // 上传信息
            $table->boolean('is_user_uploaded')->default(false)->comment('是否用户上传（0=否，1=是）');
            $table->bigInteger('uploaded_by')->unsigned()->nullable()->comment('上传用户ID，关联p_users表');
            
            // 扩展信息
            $table->json('tags')->nullable()->comment('动作标签（JSON数组）');
            $table->integer('usage_count')->default(0)->comment('使用次数');
            $table->integer('sort_order')->default(0)->comment('排序权重');
            
            // 时间戳
            $table->timestamps();
            
            // 索引
            $table->index('camera_shot', 'idx_camera_shot');
            $table->index('action_type', 'idx_action_type');
            $table->index('difficulty_level', 'idx_difficulty_level');
            $table->index('is_user_uploaded', 'idx_is_user_uploaded');
            $table->index('uploaded_by', 'idx_uploaded_by');
            $table->index('usage_count', 'idx_usage_count');
            $table->index('sort_order', 'idx_sort_order');
            
            // 外键约束
            $table->foreign('uploaded_by')->references('id')->on('p_users')->onDelete('set null');
            
            $table->comment('分镜动作库表');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('p_storyboard_action_library');
    }
};
