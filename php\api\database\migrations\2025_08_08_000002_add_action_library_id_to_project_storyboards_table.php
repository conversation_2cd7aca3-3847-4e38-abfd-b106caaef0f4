<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

/**
 * 为项目分镜表添加分镜动作库关联字段
 * 
 * 🎬 功能说明：
 * ✅ 添加action_library_id字段关联分镜动作库
 * ✅ 设置外键约束确保数据完整性
 * ✅ 支持可选关联（NULL值允许）
 * ✅ 级联删除策略：动作库删除时设为NULL
 */
return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('p_project_storyboards', function (Blueprint $table) {
            // 添加分镜动作库关联字段
            $table->bigInteger('action_library_id')->unsigned()->nullable()->after('generated_video_id')->comment('分镜动作库ID，关联p_storyboard_action_library表');
            
            // 添加索引
            $table->index('action_library_id', 'idx_action_library_id');
            
            // 添加外键约束
            $table->foreign('action_library_id')
                  ->references('id')
                  ->on('p_storyboard_action_library')
                  ->onDelete('set null')
                  ->onUpdate('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('p_project_storyboards', function (Blueprint $table) {
            // 删除外键约束
            $table->dropForeign(['action_library_id']);
            
            // 删除索引
            $table->dropIndex('idx_action_library_id');
            
            // 删除字段
            $table->dropColumn('action_library_id');
        });
    }
};
