<?php

namespace App\Services\PyApi;

use App\Models\Project;
use App\Models\ProjectCharacter;
use App\Enums\ApiCodeEnum;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Helpers\LogCheckHelper;

/**
 * 项目角色业务服务层
 * 处理项目角色相关的业务逻辑
 */
class ProjectCharacterService
{
    /**
     * 从故事内容提取角色信息
     * 这是项目创建流程中的核心方法
     */
    public function extractCharactersFromStory(int $projectId, string $storyContent): array
    {
        try {
            DB::beginTransaction();

            // 调用AI服务分析故事内容，提取角色信息
            $charactersData = $this->analyzeStoryCharacters($storyContent);

            $createdCharacters = [];
            
            foreach ($charactersData as $characterData) {
                $character = ProjectCharacter::create([
                    'project_id' => $projectId,
                    'name' => $characterData['name'],
                    'description' => $characterData['description'] ?? '',
                    'role_type' => $characterData['role_type'] ?? 'supporting',
                    'importance' => $characterData['importance'] ?? 'medium',
                    'binding_status' => ProjectCharacter::STATUS_UNBOUND
                ]);

                $createdCharacters[] = $character;
            }

            DB::commit();

            Log::info('故事角色提取成功', [
                'project_id' => $projectId,
                'character_count' => count($createdCharacters)
            ]);

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '故事角色提取成功',
                'data' => [
                    'characters' => $createdCharacters,
                    'count' => count($createdCharacters)
                ]
            ];

        } catch (\Exception $e) {
            DB::rollBack();

            Log::error('故事角色提取失败', [
                'method' => __METHOD__,
                'project_id' => $projectId,
                'error' => $e->getMessage()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '故事角色提取失败',
                'data' => []
            ];
        }
    }

    /**
     * 获取项目角色列表
     */
    public function getProjectCharacters(int $projectId, int $userId): array
    {
        try {
            // 验证项目权限
            $project = Project::where('id', $projectId)
                ->where('user_id', $userId)
                ->first();

            if (!$project) {
                return [
                    'code' => ApiCodeEnum::NOT_FOUND,
                    'message' => '项目不存在或无权访问',
                    'data' => []
                ];
            }

            $characters = ProjectCharacter::where('project_id', $projectId)
                ->orderBy('importance', 'desc')
                ->orderBy('created_at', 'asc')
                ->get();

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '获取项目角色成功',
                'data' => [
                    'project_id' => $projectId,
                    'characters' => $characters
                ]
            ];

        } catch (\Exception $e) {
            Log::error('获取项目角色失败', [
                'method' => __METHOD__,
                'project_id' => $projectId,
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '获取项目角色失败',
                'data' => []
            ];
        }
    }

    /**
     * 更新角色绑定状态
     */
    public function updateCharacterBinding(int $characterId, int $userId, ?int $characterLibraryId): array
    {
        try {
            DB::beginTransaction();

            $character = ProjectCharacter::with('project')->find($characterId);

            if (!$character) {
                return [
                    'code' => ApiCodeEnum::NOT_FOUND,
                    'message' => '角色不存在',
                    'data' => []
                ];
            }

            // 检查权限
            if ($character->project->user_id !== $userId) {
                return [
                    'code' => ApiCodeEnum::FORBIDDEN,
                    'message' => '无权修改该角色',
                    'data' => []
                ];
            }

            // 更新绑定信息（支持绑定和解绑）
            if (is_null($characterLibraryId)) {
                // 解绑
                $character->unbind();
            } else {
                // 绑定
                $character->bindCharacter($characterLibraryId);
            }

            DB::commit();

            Log::info('角色绑定更新成功', [
                'character_id' => $characterId,
                'character_library_id' => $characterLibraryId,
                'user_id' => $userId
            ]);

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '角色绑定更新成功',
                'data' => ['character' => $character->fresh()]
            ];

        } catch (\Exception $e) {
            DB::rollBack();

            Log::error('角色绑定更新失败', [
                'method' => __METHOD__,
                'character_id' => $characterId,
                'user_id' => $userId,
                'character_library_id' => $characterLibraryId,
                'error' => $e->getMessage()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '角色绑定更新失败',
                'data' => []
            ];
        }
    }

    /**
     * 一键智能匹配所有角色
     */
    public function autoMatchAllCharacters(int $projectId, int $userId): array
    {
        try {
            DB::beginTransaction();

            // 验证项目权限
            $project = Project::where('id', $projectId)
                ->where('user_id', $userId)
                ->first();

            if (!$project) {
                return [
                    'code' => ApiCodeEnum::NOT_FOUND,
                    'message' => '项目不存在或无权访问',
                    'data' => []
                ];
            }

            // 获取未绑定的角色
            $unboundCharacters = ProjectCharacter::where('project_id', $projectId)
                ->where('binding_status', ProjectCharacter::STATUS_UNBOUND)
                ->get();

            if ($unboundCharacters->isEmpty()) {
                return [
                    'code' => ApiCodeEnum::SUCCESS,
                    'message' => '所有角色已绑定',
                    'data' => ['matched_count' => 0]
                ];
            }

            $matchedCount = 0;

            foreach ($unboundCharacters as $character) {
                // 模拟智能匹配过程
                $matchedCharacterId = $this->findBestMatchCharacter($character);
                
                if ($matchedCharacterId) {
                    $character->character_library_id = $matchedCharacterId;
                    $character->binding_status = ProjectCharacter::STATUS_BOUND;
                    $character->save();
                    $matchedCount++;
                }
            }

            DB::commit();

            Log::info('一键角色匹配完成', [
                'project_id' => $projectId,
                'user_id' => $userId,
                'matched_count' => $matchedCount,
                'total_count' => $unboundCharacters->count()
            ]);

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => "一键匹配完成，成功匹配{$matchedCount}个角色",
                'data' => [
                    'matched_count' => $matchedCount,
                    'total_count' => $unboundCharacters->count()
                ]
            ];

        } catch (\Exception $e) {
            DB::rollBack();

            Log::error('一键角色匹配失败', [
                'method' => __METHOD__,
                'project_id' => $projectId,
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '一键角色匹配失败',
                'data' => []
            ];
        }
    }

    /**
     * 分析故事内容，提取角色信息
     * 实际项目中应该调用AI服务
     */
    private function analyzeStoryCharacters(string $storyContent): array
    {
        // 模拟AI分析结果
        // 实际项目中这里应该调用AI服务进行角色分析
        
        $mockCharacters = [
            [
                'name' => '主角',
                'description' => '故事的主人公，勇敢而坚定',
                'role_type' => 'protagonist',
                'importance' => 'high'
            ],
            [
                'name' => '配角',
                'description' => '主角的好友，提供帮助和支持',
                'role_type' => 'supporting',
                'importance' => 'medium'
            ],
            [
                'name' => '反派',
                'description' => '故事的对立角色，制造冲突',
                'role_type' => 'antagonist',
                'importance' => 'high'
            ]
        ];

        return $mockCharacters;
    }

    /**
     * 为角色寻找最佳匹配
     * 实际项目中应该基于角色描述和角色库进行智能匹配
     */
    private function findBestMatchCharacter(ProjectCharacter $character): ?int
    {
        // 模拟匹配逻辑
        // 实际项目中这里应该基于角色描述、类型等信息在角色库中寻找最佳匹配
        
        // 模拟返回一个角色库ID
        return rand(1, 100);
    }
}
