<?php

namespace App\Http\Controllers\PyApi;

use App\Enums\ApiCodeEnum;
use App\Services\PyApi\StoryboardNarrationService;
use App\Services\AuthService;
use App\Http\Controllers\Controller;
use App\Helpers\LogCheckHelper;
use Illuminate\Support\Facades\Log;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\Rule;
use App\Models\StoryboardNarration;

/**
 * 分镜旁白/解说控制器
 * 
 * 🚨 架构边界规范：
 * ✅ 统一使用 PyApi\AuthService 认证
 * ✅ 仅返回URL与元数据，禁止中转音频资源
 * ✅ 遵循路由排序方式与API文档序号重排
 */
class StoryboardNarrationController extends Controller
{
    protected StoryboardNarrationService $narrationService;

    public function __construct(StoryboardNarrationService $narrationService)
    {
        $this->narrationService = $narrationService;
    }

    /**
     * @ApiTitle(设置分镜解说角色)
     * @ApiSummary(为指定分镜设置解说角色，支持自定义旁白文本：需要Token认证)
     * @ApiMethod(PUT)
     * @ApiRoute(/py-api/storyboards/{id}/narration)
     * @ApiHeaders(name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     * @ApiParams(name="narrator_character_id", type="int", required=true, description="解说角色ID（项目角色）")
     * @ApiParams(name="subtitle_text", type="string", required=false, description="旁白文本（可选，不传则默认沿用分镜旁白文本）")
     * @ApiReturnParams(name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams(name="message", type="string", required=true, description="消息")
     * @ApiReturnParams(name="data", type="object", required=true, description="数据")
     * @ApiReturnParams(name="data.id", type="int", required=true, description="旁白配置ID")
     * @ApiReturnParams(name="data.storyboard_id", type="int", required=true, description="分镜ID")
     * @ApiReturnParams(name="data.narrator_character_id", type="int", required=true, description="解说角色ID")
     * @ApiReturnParams(name="data.voice_id", type="string", required=false, description="音色ID")
     * @ApiReturnParams(name="data.platform", type="string", required=true, description="AI平台")
     * @ApiReturnParams(name="data.language", type="string", required=true, description="语言")
     * @ApiReturnParams(name="data.emotion", type="string", required=true, description="情绪")
     * @ApiReturnParams(name="data.speed", type="float", required=true, description="语速")
     * @ApiReturnParams(name="data.pitch", type="float", required=true, description="音调")
     * @ApiReturnParams(name="data.subtitle_text", type="string", required=false, description="旁白文本")
     * @ApiReturnParams(name="data.effective_subtitle_text", type="string", required=false, description="有效旁白文本")
     * @ApiReturnParams(name="data.status", type="string", required=true, description="状态")
     * @ApiReturnParams(name="data.last_preview_url", type="string", required=false, description="最后试听URL")
     * @ApiReturnParams(name="data.has_voice_configured", type="boolean", required=true, description="是否已配置音色")
     * @ApiReturnParams(name="data.has_narrator_configured", type="boolean", required=true, description="是否已配置解说角色")
     * @ApiReturnParams(name="data.narrator_character", type="object", required=false, description="解说角色详情")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "解说角色设置成功",
     *   "data": {
     *     "id": 1,
     *     "storyboard_id": 123,
     *     "narrator_character_id": 456,
     *     "voice_id": null,
     *     "platform": "volcengine",
     *     "language": "zh-CN",
     *     "emotion": "neutral",
     *     "speed": 1.0,
     *     "pitch": 1.0,
     *     "subtitle_text": "这是自定义的旁白文本",
     *     "effective_subtitle_text": "这是自定义的旁白文本",
     *     "status": "active",
     *     "last_preview_url": null,
     *     "has_voice_configured": false,
     *     "has_narrator_configured": true,
     *     "narrator_character": {
     *       "id": 456,
     *       "name": "角色名称",
     *       "description": "角色描述"
     *     }
     *   }
     * })
     */
    public function setNarrationCharacter(int $id, Request $request): JsonResponse
    {
        try {
            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
            }

            // 获取用户信息
            $user = $authResult['user'];

            // 参数校验
            $validated = $request->validate([
                'narrator_character_id' => 'required|integer|min:1',
                'subtitle_text' => 'nullable|string|max:2000'
            ]);

            // 调用服务层处理业务
            $result = $this->narrationService->setNarrationCharacter(
                $id,
                $user->id,
                $validated['narrator_character_id'],
                $validated['subtitle_text'] ?? null
            );

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                // 处理成功
                return $this->successResponse($result['data'], $result['message']);
            } else {
                // 处理失败
                return $this->errorResponse($result['code'], $result['message'], $result['data']);
            }

        } catch (\Illuminate\Validation\ValidationException $e) {
            return $this->errorResponse(ApiCodeEnum::VALIDATION_ERROR, '参数验证失败', $e->errors());
        } catch (\Exception $e) {
            Log::error('设置分镜解说角色失败', [
                'method' => __METHOD__,
                'user_id' => $user->id ?? null,
                'storyboard_id' => $id,
                'error_context' => LogCheckHelper::sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '设置分镜解说角色失败', null);
        }
    }

    /**
     * @ApiTitle(设置分镜旁白音色)
     * @ApiSummary(为指定分镜的旁白设置音色参数，支持语言情绪语速音调配置：需要Token认证)
     * @ApiMethod(PUT)
     * @ApiRoute(/py-api/storyboards/{id}/narration/voice)
     * @ApiHeaders(name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     * @ApiParams(name="voice_id", type="string", required=true, description="音色ID")
     * @ApiParams(name="platform", type="string", required=false, description="AI平台（默认volcengine）")
     * @ApiParams(name="language", type="string", required=false, description="语言（zh-CN/en-US/ja-JP/ko-KR，默认zh-CN）")
     * @ApiParams(name="emotion", type="string", required=false, description="情绪（neutral/happy/sad/angry/excited/calm/energetic，默认neutral）")
     * @ApiParams(name="speed", type="float", required=false, description="语速（0.5-2.0，默认1.0）")
     * @ApiParams(name="pitch", type="float", required=false, description="音调（0.5-2.0，默认1.0）")
     * @ApiReturnParams(name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams(name="message", type="string", required=true, description="消息")
     * @ApiReturnParams(name="data", type="object", required=true, description="数据")
     * @ApiReturnParams(name="data.id", type="int", required=true, description="旁白配置ID")
     * @ApiReturnParams(name="data.storyboard_id", type="int", required=true, description="分镜ID")
     * @ApiReturnParams(name="data.narrator_character_id", type="int", required=false, description="解说角色ID")
     * @ApiReturnParams(name="data.voice_id", type="string", required=true, description="音色ID")
     * @ApiReturnParams(name="data.platform", type="string", required=true, description="AI平台")
     * @ApiReturnParams(name="data.language", type="string", required=true, description="语言")
     * @ApiReturnParams(name="data.emotion", type="string", required=true, description="情绪")
     * @ApiReturnParams(name="data.speed", type="float", required=true, description="语速")
     * @ApiReturnParams(name="data.pitch", type="float", required=true, description="音调")
     * @ApiReturnParams(name="data.subtitle_text", type="string", required=false, description="旁白文本")
     * @ApiReturnParams(name="data.effective_subtitle_text", type="string", required=false, description="有效旁白文本")
     * @ApiReturnParams(name="data.status", type="string", required=true, description="状态")
     * @ApiReturnParams(name="data.last_preview_url", type="string", required=false, description="最后试听URL")
     * @ApiReturnParams(name="data.has_voice_configured", type="boolean", required=true, description="是否已配置音色")
     * @ApiReturnParams(name="data.has_narrator_configured", type="boolean", required=true, description="是否已配置解说角色")
     * @ApiReturnParams(name="data.narrator_character", type="object", required=false, description="解说角色详情")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "音色设置成功",
     *   "data": {
     *     "id": 1,
     *     "storyboard_id": 123,
     *     "narrator_character_id": 456,
     *     "voice_id": "zh_female_qingxin",
     *     "platform": "volcengine",
     *     "language": "zh-CN",
     *     "emotion": "happy",
     *     "speed": 1.2,
     *     "pitch": 1.0,
     *     "subtitle_text": "这是旁白文本",
     *     "effective_subtitle_text": "这是旁白文本",
     *     "status": "active",
     *     "last_preview_url": null,
     *     "has_voice_configured": true,
     *     "has_narrator_configured": true,
     *     "narrator_character": {
     *       "id": 456,
     *       "name": "角色名称",
     *       "description": "角色描述"
     *     }
     *   }
     * })
     */
    public function setNarrationVoice(int $id, Request $request): JsonResponse
    {
        try {
            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
            }

            // 获取用户信息
            $user = $authResult['user'];

            // 参数校验
            $validated = $request->validate([
                'voice_id' => 'required|string|max:100',
                'platform' => 'nullable|string|max:50',
                'language' => [
                    'nullable',
                    'string',
                    Rule::in(StoryboardNarration::SUPPORTED_LANGUAGES)
                ],
                'emotion' => [
                    'nullable',
                    'string',
                    Rule::in(StoryboardNarration::SUPPORTED_EMOTIONS)
                ],
                'speed' => 'nullable|numeric|between:0.5,2.0',
                'pitch' => 'nullable|numeric|between:0.5,2.0'
            ]);

            $options = array_filter([
                'platform' => $validated['platform'] ?? null,
                'language' => $validated['language'] ?? null,
                'emotion' => $validated['emotion'] ?? null,
                'speed' => $validated['speed'] ?? null,
                'pitch' => $validated['pitch'] ?? null
            ], function ($value) {
                return $value !== null;
            });

            // 调用服务层处理业务
            $result = $this->narrationService->setNarrationVoice(
                $id,
                $user->id,
                $validated['voice_id'],
                $options
            );

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                // 处理成功
                return $this->successResponse($result['data'], $result['message']);
            } else {
                // 处理失败
                return $this->errorResponse($result['code'], $result['message'], $result['data']);
            }

        } catch (\Illuminate\Validation\ValidationException $e) {
            return $this->errorResponse(ApiCodeEnum::VALIDATION_ERROR, '参数验证失败', $e->errors());
        } catch (\Exception $e) {
            Log::error('设置分镜旁白音色失败', [
                'method' => __METHOD__,
                'user_id' => $user->id ?? null,
                'storyboard_id' => $id,
                'error_context' => LogCheckHelper::sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '设置分镜旁白音色失败', null);
        }
    }

    /**
     * @ApiTitle(获取分镜旁白配置)
     * @ApiSummary(获取指定分镜的旁白配置详情，包含解说角色和音色设置：需要Token认证)
     * @ApiMethod(GET)
     * @ApiRoute(/py-api/storyboards/{id}/narration)
     * @ApiHeaders(name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     * @ApiReturnParams(name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams(name="message", type="string", required=true, description="消息")
     * @ApiReturnParams(name="data", type="object", required=false, description="数据（如果未配置旁白则为null）")
     * @ApiReturnParams(name="data.id", type="int", required=true, description="旁白配置ID")
     * @ApiReturnParams(name="data.storyboard_id", type="int", required=true, description="分镜ID")
     * @ApiReturnParams(name="data.narrator_character_id", type="int", required=false, description="解说角色ID")
     * @ApiReturnParams(name="data.voice_id", type="string", required=false, description="音色ID")
     * @ApiReturnParams(name="data.platform", type="string", required=true, description="AI平台")
     * @ApiReturnParams(name="data.language", type="string", required=true, description="语言")
     * @ApiReturnParams(name="data.emotion", type="string", required=true, description="情绪")
     * @ApiReturnParams(name="data.speed", type="float", required=true, description="语速")
     * @ApiReturnParams(name="data.pitch", type="float", required=true, description="音调")
     * @ApiReturnParams(name="data.subtitle_text", type="string", required=false, description="旁白文本")
     * @ApiReturnParams(name="data.effective_subtitle_text", type="string", required=false, description="有效旁白文本")
     * @ApiReturnParams(name="data.status", type="string", required=true, description="状态")
     * @ApiReturnParams(name="data.last_preview_url", type="string", required=false, description="最后试听URL")
     * @ApiReturnParams(name="data.has_voice_configured", type="boolean", required=true, description="是否已配置音色")
     * @ApiReturnParams(name="data.has_narrator_configured", type="boolean", required=true, description="是否已配置解说角色")
     * @ApiReturnParams(name="data.narrator_character", type="object", required=false, description="解说角色详情")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "获取成功",
     *   "data": {
     *     "id": 1,
     *     "storyboard_id": 123,
     *     "narrator_character_id": 456,
     *     "voice_id": "zh_female_qingxin",
     *     "platform": "volcengine",
     *     "language": "zh-CN",
     *     "emotion": "neutral",
     *     "speed": 1.0,
     *     "pitch": 1.0,
     *     "subtitle_text": "这是旁白文本",
     *     "effective_subtitle_text": "这是旁白文本",
     *     "status": "active",
     *     "last_preview_url": "https://example.com/preview.mp3",
     *     "has_voice_configured": true,
     *     "has_narrator_configured": true,
     *     "narrator_character": {
     *       "id": 456,
     *       "name": "角色名称",
     *       "description": "角色描述"
     *     }
     *   }
     * })
     */
    public function getNarration(int $id, Request $request): JsonResponse
    {
        try {
            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
            }

            // 获取用户信息
            $user = $authResult['user'];

            // 调用服务层处理业务
            $result = $this->narrationService->getNarration($id, $user->id);

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                // 处理成功
                return $this->successResponse($result['data'], $result['message']);
            } else {
                // 处理失败
                return $this->errorResponse($result['code'], $result['message'], $result['data']);
            }

        } catch (\Exception $e) {
            Log::error('获取分镜旁白配置失败', [
                'method' => __METHOD__,
                'user_id' => $user->id ?? null,
                'storyboard_id' => $id,
                'error_context' => LogCheckHelper::sanitize_request_for_log([]),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '获取分镜旁白配置失败', null);
        }
    }
}
