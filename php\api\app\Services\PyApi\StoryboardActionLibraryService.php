<?php

namespace App\Services\PyApi;

use App\Enums\ApiCodeEnum;
use App\Helpers\LogCheckHelper;
use App\Models\StoryboardActionLibrary;
use App\Models\ProjectStoryboard;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;

/**
 * 分镜动作库服务层
 */
class StoryboardActionLibraryService
{
    /**
     * 获取动作库列表（分页）
     *
     * @param array $filters 筛选条件
     * @param int $perPage 每页数量
     * @param int|null $storyboardId 分镜ID，用于智能推荐排序
     * @return array
     */
    public function getActionList(array $filters = [], int $perPage = 15, ?int $storyboardId = null): array
    {
        try {
            $query = StoryboardActionLibrary::query();

            // 应用筛选条件
            $this->applyFilters($query, $filters);

            // 智能推荐排序：如果提供了分镜ID，则基于TAG匹配进行推荐排序
            if ($storyboardId) {
                $this->applySmartRecommendation($query, $storyboardId);
            } else {
                // 默认排序：按使用次数和排序权重
                $query->orderBy('usage_count', 'desc')
                      ->orderBy('sort_order', 'asc');
            }

            $actions = $query->paginate($perPage);

            // 如果有分镜ID，为每个动作添加推荐标记
            if ($storyboardId) {
                $this->markRecommendedActions($actions->items(), $storyboardId);
            }

            // 返回给控制器数据规划
            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '动作库列表获取成功',
                'data' => [
                    'data' => $actions->items(),
                    'pagination' => [
                        'current_page' => $actions->currentPage(),
                        'last_page' => $actions->lastPage(),
                        'per_page' => $actions->perPage(),
                        'total' => $actions->total()
                    ]
                ]
            ];

        } catch (\Exception $e) {
            $error_context = [
                'filters' => $filters,
                'per_page' => $perPage,
            ];

            Log::error('动作库列表获取失败', [
                'method' => __METHOD__,
                'error_context' => LogCheckHelper::sanitize_request_for_log($error_context),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '动作库列表获取失败',
                'data' => null
            ];
        }
    }

    /**
     * 根据ID获取动作详情
     *
     * @param int $id 动作ID
     * @return array
     */
    public function getActionById(int $id): array
    {
        try {
            $action = StoryboardActionLibrary::find($id);

            if (!$action) {
                return [
                    'code' => ApiCodeEnum::NOT_FOUND,
                    'message' => '动作不存在',
                    'data' => null
                ];
            }

            // 返回给控制器数据规划
            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '动作详情获取成功',
                'data' => $action
            ];

        } catch (\Exception $e) {
            $error_context = [
                'action_id' => $id,
            ];

            Log::error('动作详情获取失败', [
                'method' => __METHOD__,
                'error_context' => LogCheckHelper::sanitize_request_for_log($error_context),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '动作详情获取失败',
                'data' => null
            ];
        }
    }

    /**
     * 创建新动作
     *
     * @param array $data 动作数据
     * @return array
     */
    public function createAction(array $data): array
    {
        try {
            DB::beginTransaction();

            // 处理标签数据
            if (isset($data['tags']) && is_string($data['tags'])) {
                $data['tags'] = json_decode($data['tags'], true) ?: [];
            }

            $action = StoryboardActionLibrary::create($data);

            DB::commit();

            // 返回给控制器数据规划
            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '动作创建成功',
                'data' => $action
            ];

        } catch (\Exception $e) {
            DB::rollBack();

            $error_context = [
                'action_data' => $data,
            ];

            Log::error('动作创建失败', [
                'method' => __METHOD__,
                'error_context' => LogCheckHelper::sanitize_request_for_log($error_context),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '动作创建失败',
                'data' => null
            ];
        }
    }

    /**
     * 更新动作信息
     *
     * @param int $id 动作ID
     * @param array $data 更新数据
     * @return array
     */
    public function updateAction(int $id, array $data): array
    {
        try {
            DB::beginTransaction();

            $action = StoryboardActionLibrary::find($id);

            if (!$action) {
                DB::rollBack();
                return [
                    'code' => ApiCodeEnum::NOT_FOUND,
                    'message' => '动作不存在',
                    'data' => null
                ];
            }

            // 处理标签数据
            if (isset($data['tags']) && is_string($data['tags'])) {
                $data['tags'] = json_decode($data['tags'], true) ?: [];
            }

            $updated = $action->update($data);

            DB::commit();

            // 返回给控制器数据规划
            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '动作更新成功',
                'data' => $updated
            ];

        } catch (\Exception $e) {
            DB::rollBack();

            $error_context = [
                'action_id' => $id,
                'update_data' => $data,
            ];

            Log::error('动作更新失败', [
                'method' => __METHOD__,
                'error_context' => LogCheckHelper::sanitize_request_for_log($error_context),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '动作更新失败',
                'data' => null
            ];
        }
    }

    /**
     * 删除动作
     *
     * @param int $id 动作ID
     * @return array
     */
    public function deleteAction(int $id): array
    {
        try {
            $action = StoryboardActionLibrary::find($id);

            if (!$action) {
                return [
                    'code' => ApiCodeEnum::NOT_FOUND,
                    'message' => '动作不存在',
                    'data' => null
                ];
            }

            $result = $action->delete();

            // 返回给控制器数据规划
            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '动作删除成功',
                'data' => $result
            ];

        } catch (\Exception $e) {
            $error_context = [
                'action_id' => $id,
            ];

            Log::error('动作删除失败', [
                'method' => __METHOD__,
                'error_context' => LogCheckHelper::sanitize_request_for_log($error_context),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '动作删除失败',
                'data' => null
            ];
        }
    }

    /**
     * 增加动作使用次数
     *
     * @param int $id 动作ID
     * @return array
     */
    public function incrementUsage(int $id): array
    {
        try {
            $action = StoryboardActionLibrary::find($id);

            if (!$action) {
                return [
                    'code' => ApiCodeEnum::NOT_FOUND,
                    'message' => '动作不存在',
                    'data' => null
                ];
            }

            $action->incrementUsage();

            // 返回给控制器数据规划
            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '使用次数增加成功',
                'data' => true
            ];

        } catch (\Exception $e) {
            $error_context = [
                'action_id' => $id,
            ];

            Log::error('使用次数增加失败', [
                'method' => __METHOD__,
                'error_context' => LogCheckHelper::sanitize_request_for_log($error_context),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '使用次数增加失败',
                'data' => null
            ];
        }
    }

    /**
     * 搜索动作
     *
     * @param string $keyword 搜索关键词
     * @param array $filters 筛选条件
     * @param int $perPage 每页数量
     * @return array
     */
    public function searchActions(string $keyword, array $filters = [], int $perPage = 15): array
    {
        try {
            $query = StoryboardActionLibrary::query();

            // 关键词搜索
            if (!empty($keyword)) {
                $query->where(function ($q) use ($keyword) {
                    $q->where('title', 'like', "%{$keyword}%")
                      ->orWhere('description', 'like', "%{$keyword}%")
                      ->orWhere('action_type', 'like', "%{$keyword}%");
                });
            }

            // 应用筛选条件
            $this->applyFilters($query, $filters);

            $results = $query->orderBy('usage_count', 'desc')
                            ->orderBy('sort_order', 'asc')
                            ->paginate($perPage);

            // 返回给控制器数据规划
            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '动作搜索成功',
                'data' => [
                    'data' => $results->items(),
                    'pagination' => [
                        'current_page' => $results->currentPage(),
                        'last_page' => $results->lastPage(),
                        'per_page' => $results->perPage(),
                        'total' => $results->total()
                    ]
                ]
            ];

        } catch (\Exception $e) {
            $error_context = [
                'keyword' => $keyword,
                'filters' => $filters,
                'per_page' => $perPage,
            ];

            Log::error('动作搜索失败', [
                'method' => __METHOD__,
                'error_context' => LogCheckHelper::sanitize_request_for_log($error_context),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '动作搜索失败',
                'data' => null
            ];
        }
    }

    /**
     * 根据镜头类型获取动作
     *
     * @param string $cameraShot 镜头类型
     * @param int $limit 返回数量限制
     * @return array
     */
    public function getActionsByCameraShot(string $cameraShot, int $limit = 20): array
    {
        try {
            $actions = StoryboardActionLibrary::byCameraShot($cameraShot)
                ->orderBy('usage_count', 'desc')
                ->orderBy('sort_order', 'asc')
                ->limit($limit)
                ->get();

            // 返回给控制器数据规划
            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '镜头类型动作获取成功',
                'data' => $actions
            ];

        } catch (\Exception $e) {
            $error_context = [
                'camera_shot' => $cameraShot,
                'limit' => $limit,
            ];

            Log::error('镜头类型动作获取失败', [
                'method' => __METHOD__,
                'error_context' => LogCheckHelper::sanitize_request_for_log($error_context),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '镜头类型动作获取失败',
                'data' => null
            ];
        }
    }

    /**
     * 获取动作统计信息
     *
     * @return array
     */
    public function getActionStatistics(): array
    {
        try {
            $total = StoryboardActionLibrary::count();
            $userUploaded = StoryboardActionLibrary::userUploaded()->count();

            // 按镜头类型统计
            $byCameraShot = StoryboardActionLibrary::select('camera_shot', DB::raw('count(*) as count'))
                ->groupBy('camera_shot')
                ->pluck('count', 'camera_shot')
                ->toArray();

            // 按难度等级统计
            $byDifficulty = StoryboardActionLibrary::select('difficulty_level', DB::raw('count(*) as count'))
                ->groupBy('difficulty_level')
                ->pluck('count', 'difficulty_level')
                ->toArray();

            $statistics = [
                'total' => $total,
                'user_uploaded' => $userUploaded,
                'by_camera_shot' => $byCameraShot,
                'by_difficulty' => $byDifficulty
            ];

            // 返回给控制器数据规划
            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '统计信息获取成功',
                'data' => $statistics
            ];

        } catch (\Exception $e) {
            Log::error('统计信息获取失败', [
                'method' => __METHOD__,
                'error_context' => LogCheckHelper::sanitize_request_for_log([]),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '统计信息获取失败',
                'data' => null
            ];
        }
    }

    /**
     * 获取动作选项数据
     *
     * @return array
     */
    public function getActionOptions(): array
    {
        try {
            $options = [
                'camera_shot' => StoryboardActionLibrary::getCameraShotOptions(),
                'difficulty_level' => StoryboardActionLibrary::getDifficultyOptions()
            ];

            // 返回给控制器数据规划
            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '选项数据获取成功',
                'data' => $options
            ];

        } catch (\Exception $e) {
            Log::error('选项数据获取失败', [
                'method' => __METHOD__,
                'error_context' => LogCheckHelper::sanitize_request_for_log([]),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '选项数据获取失败',
                'data' => null
            ];
        }
    }

    /**
     * 应用筛选条件
     *
     * @param $query 查询构建器
     * @param array $filters 筛选条件
     * @return void
     */
    private function applyFilters($query, array $filters): void
    {
        if (!empty($filters['camera_shot'])) {
            $query->byCameraShot($filters['camera_shot']);
        }

        if (!empty($filters['difficulty_level'])) {
            $query->byDifficulty($filters['difficulty_level']);
        }

        if (isset($filters['is_user_uploaded'])) {
            if ($filters['is_user_uploaded']) {
                $query->userUploaded();
            } else {
                $query->systemPreset();
            }
        }

        if (!empty($filters['uploaded_by'])) {
            $query->where('uploaded_by', $filters['uploaded_by']);
        }
    }

    /**
     * 应用智能推荐排序
     * 基于分镜的ai_prompt字段中的关键词与动作库tags字段进行匹配
     *
     * @param $query 查询构建器
     * @param int $storyboardId 分镜ID
     * @return void
     */
    private function applySmartRecommendation($query, int $storyboardId): void
    {
        try {
            // 获取分镜的ai_prompt
            $storyboard = ProjectStoryboard::find($storyboardId);
            if (!$storyboard || empty($storyboard->ai_prompt)) {
                // 如果没有ai_prompt，使用默认排序
                $query->orderBy('usage_count', 'desc')
                      ->orderBy('sort_order', 'asc');
                return;
            }

            $aiPrompt = strtolower($storyboard->ai_prompt);

            // 使用原生SQL进行复杂的推荐排序
            $query->select('*')
                  ->selectRaw('
                      CASE
                          WHEN JSON_SEARCH(LOWER(tags), "one", ?) IS NOT NULL THEN 1
                          ELSE 0
                      END as is_recommended
                  ', [$aiPrompt])
                  ->orderByRaw('is_recommended DESC')
                  ->orderBy('usage_count', 'desc')
                  ->orderBy('sort_order', 'asc');

        } catch (\Exception $e) {
            Log::error('智能推荐排序失败', [
                'method' => __METHOD__,
                'storyboard_id' => $storyboardId,
                'error' => $e->getMessage()
            ]);

            // 出错时使用默认排序
            $query->orderBy('usage_count', 'desc')
                  ->orderBy('sort_order', 'asc');
        }
    }

    /**
     * 为动作添加推荐标记
     * 检查动作的tags是否与分镜的ai_prompt匹配
     *
     * @param array $actions 动作列表
     * @param int $storyboardId 分镜ID
     * @return void
     */
    private function markRecommendedActions(array $actions, int $storyboardId): void
    {
        try {
            // 获取分镜的ai_prompt
            $storyboard = ProjectStoryboard::find($storyboardId);
            if (!$storyboard || empty($storyboard->ai_prompt)) {
                // 如果没有ai_prompt，所有动作都不标记为推荐
                foreach ($actions as $action) {
                    $action->is_recommended = false;
                }
                return;
            }

            $aiPrompt = strtolower($storyboard->ai_prompt);

            foreach ($actions as $action) {
                $action->is_recommended = false;

                if (!empty($action->tags)) {
                    $tags = is_string($action->tags) ? json_decode($action->tags, true) : $action->tags;

                    if (is_array($tags)) {
                        foreach ($tags as $tag) {
                            if (stripos($aiPrompt, strtolower($tag)) !== false) {
                                $action->is_recommended = true;
                                break;
                            }
                        }
                    }
                }
            }

        } catch (\Exception $e) {
            Log::error('标记推荐动作失败', [
                'method' => __METHOD__,
                'storyboard_id' => $storyboardId,
                'error' => $e->getMessage()
            ]);

            // 出错时所有动作都不标记为推荐
            foreach ($actions as $action) {
                $action->is_recommended = false;
            }
        }
    }
}
