<?php

namespace App\Services;

use App\Helpers\LogCheckHelper;
use App\Enums\ApiCodeEnum;
use App\Helpers\ApiTokenHelper;
use App\Exceptions\ApiException;
use App\Exceptions\TokenException;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Redis;
use Illuminate\Support\Facades\Log;
//use Illuminate\Support\Facades\Cache;

class AuthService
{
    /**
     * @param string $username
     * @param string $password
     * @return array
     */
    public function register(string $username, string $password)
    {
        try {
            // 检查用户名是否已存在（只检查用户名，不检查密码）
            $existingUser = DB::table('users')->select('id')
                ->where('username', $username)
                ->first();

            if (!empty($existingUser)) {
                $code = ApiCodeEnum::USER_ALREADY_EXISTS;
                $message = ApiCodeEnum::getDescription($code);
                return ['code' => $code, 'message' => $message, 'data' => []];
            }

            // 使用事务确保数据一致性
            DB::beginTransaction();

            $id = DB::table('users')->insertGetId([
                'username' => $username,
                'password' => $password,
                'points' => 0.00,
                'frozen_points' => 0.00,
                'status' => 1,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ]);

            $token = ApiTokenHelper::generateToken($id);
            $ttl = mt_rand(3600 * 24 * 30, 3600 * 24 * 35);
            Redis::setex('user:token:'.$id, $ttl, ApiTokenHelper::encryptToken($token));

            DB::commit();

            $code = ApiCodeEnum::SUCCESS;
            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => ApiCodeEnum::getDescription($code),
                'data' => [
                    'id' => $id,
                    'token' => $token
                ]
            ];

        } catch (\Exception $e) {
            // 如果try块使用了"DB::beginTransaction();"请启用下面这行，否则不要应用此行和下一行
            DB::rollBack();

            $error_context = [
                'username' => $username,
            ];

            Log::error('用户注册失败', [
                'method' => __METHOD__,
                'error_context' => LogCheckHelper::sanitize_request_for_log($error_context),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '用户注册失败',
                'data' => null
            ];
        }
    }

    /**
     * @param int $username
     * @param int $password
     * @return array
     */
    public function login(string $username, string $password)
    {
        try {
            $user = DB::table('users')->select('id')
                ->where('username', $username)
                ->where('password', $password)
                ->first();
            if(empty($user))
            {
                $code = ApiCodeEnum::USER_NOT_REGISTERED;
                $message = ApiCodeEnum::getDescription($code);
                return ['code' => $code,'message' => $message,'data' => []];
            }else{
                $id = $user->id;
                $code = ApiCodeEnum::SUCCESS;
                $token = ApiTokenHelper::generateToken($id);
                $ttl = mt_rand(3600 * 24 * 30, 3600 * 24 * 35);

                $redisKey = 'user:token:'.$id;
                $encryptedToken = ApiTokenHelper::encryptToken($token);
                Redis::setex($redisKey, $ttl, $encryptedToken);

                return ['code' => $code, 'message' => 'success', 'data' => ['token' => $token, 'user' => ['id' => $id]]];
            }
        } catch (\Exception $e) {
            $error_context = [
                'username' => $username,
            ];

            Log::error('用户登录失败', [
                'method' => __METHOD__,
                'error_context' => LogCheckHelper::sanitize_request_for_log($error_context),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '用户登录失败',
                'data' => null
            ];
        }
    }

    /**
     * 通过邮箱登录 (符合dev-api-guidelines-add.mdc标准)
     * @param string $email
     * @param string $password
     * @return array
     */
    public function loginByEmail(string $email, string $password)
    {
        try {
            $user = DB::table('users')->select('id', 'username', 'email', 'nickname')
                ->where('email', $email)
                ->where('password', $password)
                ->where('status', 1)
                ->first();

            if(empty($user))
            {
                $code = ApiCodeEnum::USER_NOT_REGISTERED;
                $message = ApiCodeEnum::getDescription($code);
                return ['code' => $code,'message' => $message,'data' => []];
            }else{
                $id = $user->id;
                $code = ApiCodeEnum::SUCCESS;
                $token = ApiTokenHelper::generateToken($id);
                $ttl = mt_rand(3600 * 24 * 30, 3600 * 24 * 35);

                $redisKey = 'user:token:'.$id;
                $encryptedToken = ApiTokenHelper::encryptToken($token);
                Redis::setex($redisKey, $ttl, $encryptedToken);

                return [
                    'code' => ApiCodeEnum::SUCCESS,
                    'message' => 'success',
                    'data' => [
                        'token' => $token,
                        'user' => [
                            'id' => $id,
                            'username' => $user->username,
                            'email' => $user->email,
                            'nickname' => $user->nickname
                        ]
                    ]
                ];
            }
        } catch (\Exception $e) {
            $error_context = [
                'email' => $email,
            ];

            Log::error('邮箱登录失败', [
                'method' => __METHOD__,
                'error_context' => LogCheckHelper::sanitize_request_for_log($error_context),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '邮箱登录失败',
                'data' => null
            ];
        }
    }

    /**
     * @param $token
     * @return int
     */
    public function getAuthUserID($token)
    {
        try {
            $id = ApiTokenHelper::getUserIdByToken($token);
            if(!empty($id))
            {
                $encrypt_token = Redis::get('user:token:'.$id);
                if($encrypt_token == ApiTokenHelper::encryptToken($token))
                {
                    return $id;
                }
            }
            throw new ApiException('Token无效,请从新登录',ApiCodeEnum::INVALID_TOKEN);
        } catch (ApiException $e) {
            // 重新抛出API异常
            throw $e;
        } catch (\Exception $e) {
            $error_context = [
                'token' => substr($token, 0, 10) . '...',
            ];

            Log::error('Token验证失败', [
                'method' => __METHOD__,
                'error_context' => LogCheckHelper::sanitize_request_for_log($error_context),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            throw new ApiException('Token验证失败', ApiCodeEnum::INVALID_TOKEN);
        }
    }

    /**
     * 从请求中提取token
     *
     * @param Request $request
     * @return string|null
     */
    public static function extractToken(Request $request): ?string
    {
        // 首先尝试从请求参数中获取token
        $token = $request->input('token');

        if (empty($token)) {
            // 从Authorization头中提取token
            $header = $request->header('Authorization', '');
            $position = strrpos($header, 'Bearer ');
            if ($position !== false) {
                $header = substr($header, $position + 7);
                $token = strpos($header, ',') !== false ? strstr($header, ',', true) : $header;
            }
        }

        return $token ?: null;
    }

    /**
     * 验证token并返回用户信息
     *
     * @param string $token
     * @return object|null 返回用户信息对象或null
     */
    public static function validateToken(string $token): ?object
    {
        // 1. 检查Token是否在黑名单中
        if (self::isTokenBlacklisted($token)) {
            return null;
        }

        // 2. 解析Token并检查格式
        $tokenData = ApiTokenHelper::parseToken($token);
        if (!$tokenData) {
            return null;
        }

        $userId = $tokenData['user_id'];

        // 3. 显式检查Token过期时间
        if (ApiTokenHelper::isTokenExpired($token)) {
            // Token已过期，从Redis中清理并加入黑名单
            $redisKey = 'user:token:' . $userId;
            Redis::del($redisKey);
            self::addTokenToBlacklist($token, 'expired');
            return null;
        }

        // 4. 验证token是否在Redis中有效
        $redisKey = 'user:token:' . $userId;
        $encryptToken = Redis::get($redisKey);
        if ($encryptToken !== ApiTokenHelper::encryptToken($token)) {
            return null;
        }

        // 5. 查询用户信息
        $user = DB::table('users')->where('id', $userId)->first();
        if (!$user) {
            return null;
        }

        // 6. 检查用户状态
        if (isset($user->status) && $user->status === 'disabled') {
            // 用户被禁用，将Token加入黑名单
            self::addTokenToBlacklist($token, 'user_disabled');
            return null;
        }

        // 7. Token滑动刷新（如果启用）
        if (config('auth.token_sliding_refresh', true)) {
            self::extendTokenExpiry($userId, $token);
        }

        return $user;
    }

    /**
     * 增强的Token验证方法（带详细错误信息）
     *
     * @param string $token
     * @return array ['success' => bool, 'user' => object|null, 'error' => TokenException|null]
     */
    public static function validateTokenEnhanced(string $token): array
    {
        try {
            // 1. 检查Token是否在黑名单中
            if (self::isTokenBlacklisted($token)) {
                $blacklistKey = 'token:blacklist:' . ApiTokenHelper::encryptToken($token);
                $blacklistData = Redis::get($blacklistKey);
                $data = json_decode($blacklistData, true) ?? [];

                return [
                    'success' => false,
                    'user' => null,
                    'error' => TokenException::blacklisted($data['reason'] ?? 'unknown')
                ];
            }

            // 2. 解析Token并检查格式
            $tokenData = ApiTokenHelper::parseToken($token);
            if (!$tokenData) {
                return [
                    'success' => false,
                    'user' => null,
                    'error' => TokenException::invalidFormat($token)
                ];
            }

            $userId = $tokenData['user_id'];

            // 3. 显式检查Token过期时间
            if (ApiTokenHelper::isTokenExpired($token)) {
                // Token已过期，从Redis中清理并加入黑名单
                $redisKey = 'user:token:' . $userId;
                Redis::del($redisKey);
                self::addTokenToBlacklist($token, 'expired');

                return [
                    'success' => false,
                    'user' => null,
                    'error' => TokenException::expired($tokenData['expires_at'])
                ];
            }

            // 4. 验证token是否在Redis中有效
            $redisKey = 'user:token:' . $userId;
            $encryptToken = Redis::get($redisKey);
            if ($encryptToken !== ApiTokenHelper::encryptToken($token)) {
                return [
                    'success' => false,
                    'user' => null,
                    'error' => TokenException::notFound()
                ];
            }

            // 5. 查询用户信息
            $user = DB::table('users')->where('id', $userId)->first();
            if (!$user) {
                return [
                    'success' => false,
                    'user' => null,
                    'error' => TokenException::userNotFound($userId)
                ];
            }

            // 6. 检查用户状态
            if (isset($user->status) && $user->status === 'disabled') {
                // 用户被禁用，将Token加入黑名单
                self::addTokenToBlacklist($token, 'user_disabled');
                return [
                    'success' => false,
                    'user' => null,
                    'error' => TokenException::userDisabled($userId)
                ];
            }

            // 7. Token滑动刷新（如果启用）
            if (config('auth.token_sliding_refresh', true)) {
                self::extendTokenExpiry($userId, $token);
            }

            return [
                'success' => true,
                'user' => $user,
                'error' => null
            ];

        } catch (\Exception $e) {
            Log::error('Token验证异常', [
                'token_hint' => substr($token, 0, 8) . '...',
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'success' => false,
                'user' => null,
                'error' => TokenException::invalidFormat($token)
            ];
        }
    }

    /**
     * 检查Token是否在黑名单中
     *
     * @param string $token
     * @return bool
     */
    public static function isTokenBlacklisted(string $token): bool
    {
        $blacklistKey = 'token:blacklist:' . ApiTokenHelper::encryptToken($token);
        return Redis::exists($blacklistKey);
    }

    /**
     * 将Token加入黑名单
     *
     * @param string $token
     * @param string $reason 加入黑名单的原因
     * @param int $ttl 黑名单过期时间（秒），默认30天
     * @return bool
     */
    public static function addTokenToBlacklist(string $token, string $reason = 'manual', ?int $ttl = null): bool
    {
        $ttl = $ttl ?? (3600 * 24 * 30); // 默认30天
        $blacklistKey = 'token:blacklist:' . ApiTokenHelper::encryptToken($token);

        $blacklistData = [
            'reason' => $reason,
            'created_at' => date('Y-m-d H:i:s'),
            'token_hash' => substr(ApiTokenHelper::encryptToken($token), 0, 8) // 只存储部分hash用于日志
        ];

        return Redis::setex($blacklistKey, $ttl, json_encode($blacklistData));
    }

    /**
     * 延长Token过期时间（滑动刷新）
     *
     * @param int $userId
     * @param string $token
     * @return bool
     */
    public static function extendTokenExpiry(int $userId, string $token): bool
    {
        $maxSlidingDays = config('auth.max_sliding_days', 180); // 最大滑动时间6个月
        $slidingDays = config('auth.sliding_days', 30); // 每次延长30天

        $redisKey = 'user:token:' . $userId;
        $currentTtl = Redis::ttl($redisKey);

        // 如果当前TTL超过最大滑动时间，则不再延长
        if ($currentTtl > ($maxSlidingDays * 24 * 3600)) {
            return false;
        }

        $newTtl = mt_rand($slidingDays * 24 * 3600, ($slidingDays + 5) * 24 * 3600);

        // 记录Token使用日志
        self::logTokenUsage($userId, $token, 'sliding_refresh');

        return Redis::expire($redisKey, $newTtl);
    }

    /**
     * 记录Token使用日志
     *
     * @param int $userId
     * @param string $token
     * @param string $action
     */
    private static function logTokenUsage(int $userId, string $token, string $action): void
    {
        $logKey = 'token:usage:' . $userId . ':' . date('Y-m-d');
        $logData = [
            'action' => $action,
            'token_hash' => substr(ApiTokenHelper::encryptToken($token), 0, 8),
            'timestamp' => time(),
            'ip' => request()->ip() ?? 'unknown'
        ];

        Redis::lpush($logKey, json_encode($logData));
        Redis::expire($logKey, 3600 * 24 * 7); // 日志保留7天
    }

    /**
     * 主动使Token失效
     *
     * @param string $token
     * @param string $reason
     * @return bool
     */
    public static function revokeToken(string $token, string $reason = 'manual_revoke'): bool
    {
        $userId = ApiTokenHelper::getUserIdByToken($token);
        if (empty($userId)) {
            return false;
        }

        // 1. 从Redis中删除Token
        $redisKey = 'user:token:' . $userId;
        Redis::del($redisKey);

        // 2. 将Token加入黑名单
        self::addTokenToBlacklist($token, $reason);

        // 3. 记录操作日志
        self::logTokenUsage($userId, $token, 'revoked');

        return true;
    }

    /**
     * 使用户所有Token失效
     *
     * @param int $userId
     * @param string $reason
     * @return bool
     */
    public static function revokeAllUserTokens(int $userId, string $reason = 'revoke_all'): bool
    {
        // 1. 删除用户的access token
        $accessTokenKey = 'user:token:' . $userId;
        Redis::del($accessTokenKey);

        // 2. 删除用户的refresh token
        $refreshTokenKey = 'user:refresh_token:' . $userId;
        Redis::del($refreshTokenKey);

        // 3. 记录操作日志
        $logKey = 'token:usage:' . $userId . ':' . date('Y-m-d');
        $logData = [
            'action' => 'revoke_all_tokens',
            'reason' => $reason,
            'timestamp' => time(),
            'ip' => request()->ip() ?? 'unknown'
        ];
        Redis::lpush($logKey, json_encode($logData));

        return true;
    }

    /**
     * 获取Token状态信息
     *
     * @param string $token
     * @return array
     */
    public static function getTokenStatus(string $token): array
    {
        $userId = ApiTokenHelper::getUserIdByToken($token);

        if (empty($userId)) {
            return ['status' => 'invalid', 'reason' => 'invalid_format'];
        }

        // 检查黑名单
        if (self::isTokenBlacklisted($token)) {
            $blacklistKey = 'token:blacklist:' . ApiTokenHelper::encryptToken($token);
            $blacklistData = Redis::get($blacklistKey);
            $data = json_decode($blacklistData, true) ?? [];

            return [
                'status' => 'blacklisted',
                'reason' => $data['reason'] ?? 'unknown',
                'blacklisted_at' => $data['created_at'] ?? 'unknown'
            ];
        }

        // 检查Redis中是否存在
        $redisKey = 'user:token:' . $userId;
        $encryptToken = Redis::get($redisKey);

        if ($encryptToken !== ApiTokenHelper::encryptToken($token)) {
            return ['status' => 'expired', 'reason' => 'not_found_in_redis'];
        }

        // 获取TTL
        $ttl = Redis::ttl($redisKey);

        return [
            'status' => 'valid',
            'user_id' => $userId,
            'expires_in' => $ttl,
            'expires_at' => $ttl > 0 ? date('Y-m-d H:i:s', time() + $ttl) : 'never'
        ];
    }

    /**
     * 认证用户并返回用户信息或错误响应
     *
     * @param Request $request
     * @return array 返回 ['success' => bool, 'user' => object|null, 'response' => array|null]
     */
    public static function authenticate(Request $request): array
    {
        // 提取token
        $token = self::extractToken($request);
        if (empty($token)) {
            return [
                'success' => false,
                'user' => null,
                'response' => [
                    'code' => ApiCodeEnum::UNAUTHORIZED,
                    'message' => '请登录后操作',
                    'data' => []
                ]
            ];
        }

        // 验证token
        $user = self::validateToken($token);
        if (!$user) {
            return [
                'success' => false,
                'user' => null,
                'response' => [
                    'code' => ApiCodeEnum::UNAUTHORIZED,
                    'message' => '请登录后操作',
                    'data' => []
                ]
            ];
        }

        return [
            'success' => true,
            'user' => $user,
            'response' => null
        ];
    }

    /**
     * 快速认证方法，直接返回用户信息或抛出认证失败响应
     *
     * @param Request $request
     * @return object 用户信息对象
     * @throws \Exception 认证失败时抛出异常，包含响应数据
     */
    public static function requireAuth(Request $request): object
    {
        $result = self::authenticate($request);

        if (!$result['success']) {
            throw new \Exception(json_encode($result['response']));
        }

        return $result['user'];
    }

    /**
     * 用户登出 - 简化版本
     *
     * @param int $userId 用户ID
     * @return array
     */
    public function logout(int $userId): array
    {
        try {
            // 清除用户Token
            $redisKey = "user:token:{$userId}";
            Redis::del($redisKey);

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '登出成功',
                'data' => []
            ];

        } catch (\Exception $e) {
            $error_context = [
                'user_id' => $userId,
            ];

            Log::error('用户登出失败', [
                'method' => __METHOD__,
                'error_context' => LogCheckHelper::sanitize_request_for_log($error_context),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '用户登出失败',
                'data' => null
            ];
        }
    }

    /**
     * 忘记密码 - 简化版本
     *
     * @param string $email
     * @return array
     */
    public function forgotPassword(string $email): array
    {
        try {
            // 检查用户是否存在
            $user = DB::table('p_users')->where('email', $email)->first();
            if (!$user) {
                return [
                    'code' => ApiCodeEnum::UNAUTHORIZED,
                    'message' => '用户不存在',
                    'data' => []
                ];
            }

            // 生成重置令牌
            $token = bin2hex(random_bytes(32));

            // 存储重置令牌（有效期1小时）
            $resetKey = "password_reset:{$token}";
            Redis::setex($resetKey, 3600, $user->id);

            // 这里应该发送邮件，简化处理只返回成功
            Log::info('Password reset requested', [
                'email' => $email,
                'user_id' => $user->id,
                'token' => $token
            ]);

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '密码重置邮件发送成功',
                'data' => []
            ];

        } catch (\Exception $e) {
            $error_context = [
                'email' => $email,
            ];

            Log::error('忘记密码操作失败', [
                'method' => __METHOD__,
                'error_context' => LogCheckHelper::sanitize_request_for_log($error_context),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '忘记密码操作失败',
                'data' => null
            ];
        }
    }

    /**
     * 重置密码 - 简化版本
     *
     * @param string $token
     * @param string $newPassword
     * @return array
     */
    public function resetPassword(string $token, string $newPassword): array
    {
        try {
            // 验证重置令牌
            $resetKey = "password_reset:{$token}";
            $userId = Redis::get($resetKey);

            if (!$userId) {
                return [
                    'code' => ApiCodeEnum::UNAUTHORIZED,
                    'message' => '重置令牌无效或已过期',
                    'data' => []
                ];
            }

            // 更新密码
            $hashedPassword = password_hash($newPassword, PASSWORD_DEFAULT);
            DB::table('users')
                ->where('id', $userId)
                ->update(['password' => $hashedPassword]);

            // 删除重置令牌
            Redis::del($resetKey);

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '密码重置成功',
                'data' => []
            ];

        } catch (\Exception $e) {
            $error_context = [
                'token' => substr($token, 0, 10) . '...',
            ];

            Log::error('密码重置失败', [
                'method' => __METHOD__,
                'error_context' => LogCheckHelper::sanitize_request_for_log($error_context),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '密码重置失败',
                'data' => null
            ];
        }
    }

    /**
     * Token刷新 - 基于七重同步铁律实施
     *
     * @param string $refreshToken refresh_token
     * @return array
     */
    public function refreshToken(string $refreshToken): array
    {
        try {
            // 1. 解析refresh_token获取用户ID
            $userId = $this->getUserIdFromRefreshToken($refreshToken);
            if (!$userId) {
                return [
                    'code' => 4011, // REFRESH_TOKEN_INVALID
                    'message' => 'refresh_token无效',
                    'data' => []
                ];
            }

            // 2. 验证refresh_token有效性
            if (!$this->validateRefreshToken($refreshToken, $userId)) {
                return [
                    'code' => 4012, // REFRESH_TOKEN_EXPIRED
                    'message' => 'refresh_token过期',
                    'data' => []
                ];
            }

            // 3. 检查Token黑名单
            if ($this->isRefreshTokenBlacklisted($refreshToken)) {
                return [
                    'code' => 4013, // REFRESH_TOKEN_BLACKLISTED
                    'message' => 'refresh_token已失效',
                    'data' => []
                ];
            }

            // 4. 验证用户是否存在
            $user = DB::table('users')->where('id', $userId)->first();
            if (!$user) {
                return [
                    'code' => 4014, // REFRESH_USER_NOT_FOUND
                    'message' => '用户不存在',
                    'data' => []
                ];
            }

            // 5. 生成新的Token对
            $newAccessToken = ApiTokenHelper::generateToken($userId);
            $newRefreshToken = $this->generateRefreshToken($userId);

            // 6. 原子性更新Token
            $this->atomicTokenUpdate($userId, $newAccessToken, $newRefreshToken, $refreshToken);

            // 7. 记录刷新日志
            $this->logTokenRefresh($userId, $refreshToken, $newRefreshToken);

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => 'Token刷新成功',
                'data' => [
                    'access_token' => $newAccessToken,
                    'refresh_token' => $newRefreshToken,
                    'token_type' => 'Bearer',
                    'expires_in' => 3600 * 24 * 30, // 30天
                    'refresh_time' => date('c') // ISO 8601格式
                ]
            ];

        } catch (\Exception $e) {
            $error_context = [
                'refresh_token' => substr($refreshToken, 0, 10) . '...',
            ];

            Log::error('Token刷新失败', [
                'method' => __METHOD__,
                'error_context' => LogCheckHelper::sanitize_request_for_log($error_context),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => 'Token刷新失败',
                'data' => null
            ];
        }
    }

    /**
     * 从refresh_token中解析用户ID
     *
     * @param string $refreshToken
     * @return int|null
     */
    private function getUserIdFromRefreshToken(string $refreshToken): ?int
    {
        // 简化实现：从refresh_token中解析用户ID
        // 实际项目中可能需要更复杂的解析逻辑
        try {
            // 假设refresh_token格式为: userId_timestamp_randomString
            $parts = explode('_', $refreshToken);
            if (count($parts) >= 3 && is_numeric($parts[0])) {
                return (int)$parts[0];
            }
        } catch (\Exception $e) {
            Log::warning('Failed to parse user ID from refresh token', [
                'refresh_token' => substr($refreshToken, 0, 10) . '...',
                'error' => $e->getMessage()
            ]);
        }

        return null;
    }

    /**
     * 验证refresh_token有效性
     *
     * @param string $refreshToken
     * @param int $userId
     * @return bool
     */
    private function validateRefreshToken(string $refreshToken, int $userId): bool
    {
        $refreshTokenKey = "user:refresh_token:{$userId}";
        $storedToken = Redis::get($refreshTokenKey);

        if (!$storedToken) {
            return false;
        }

        // 解密并比较
        return $this->decryptRefreshToken($storedToken) === $refreshToken;
    }

    /**
     * 检查refresh_token是否在黑名单中
     *
     * @param string $refreshToken
     * @return bool
     */
    private function isRefreshTokenBlacklisted(string $refreshToken): bool
    {
        $blacklistKey = "refresh_token:blacklist:{$refreshToken}";
        return Redis::exists($blacklistKey);
    }

    /**
     * 生成refresh_token
     *
     * @param int $userId
     * @return string
     */
    private function generateRefreshToken(int $userId): string
    {
        $timestamp = time();
        $randomString = bin2hex(random_bytes(16));
        return "{$userId}_{$timestamp}_{$randomString}";
    }

    /**
     * 原子性Token更新
     *
     * @param int $userId
     * @param string $newAccessToken
     * @param string $newRefreshToken
     * @param string $oldRefreshToken
     */
    private function atomicTokenUpdate(int $userId, string $newAccessToken, string $newRefreshToken, string $oldRefreshToken): void
    {
        Redis::multi();

        // 更新access_token
        $accessTokenKey = "user:token:{$userId}";
        $accessTokenTtl = mt_rand(3600 * 24 * 30, 3600 * 24 * 35);
        Redis::setex($accessTokenKey, $accessTokenTtl, ApiTokenHelper::encryptToken($newAccessToken));

        // 更新refresh_token
        $refreshTokenKey = "user:refresh_token:{$userId}";
        $refreshTokenTtl = 3600 * 24 * 60; // 60天
        Redis::setex($refreshTokenKey, $refreshTokenTtl, $this->encryptRefreshToken($newRefreshToken));

        // 将旧refresh_token加入黑名单
        $blacklistKey = "refresh_token:blacklist:{$oldRefreshToken}";
        Redis::setex($blacklistKey, $refreshTokenTtl, 'used');

        Redis::exec();
    }

    /**
     * 加密refresh_token
     *
     * @param string $refreshToken
     * @return string
     */
    private function encryptRefreshToken(string $refreshToken): string
    {
        // 使用与access_token相同的加密方法
        return ApiTokenHelper::encryptToken($refreshToken);
    }

    /**
     * 解密refresh_token
     *
     * @param string $encryptedRefreshToken
     * @return string
     */
    private function decryptRefreshToken(string $encryptedRefreshToken): string
    {
        // 简化实现：实际项目中需要实现对应的解密方法
        // 这里假设加密是可逆的
        return $encryptedRefreshToken; // 临时实现
    }

    /**
     * 记录Token刷新日志
     *
     * @param int $userId
     * @param string $oldRefreshToken
     * @param string $newRefreshToken
     */
    private function logTokenRefresh(int $userId, string $oldRefreshToken, string $newRefreshToken): void
    {
        try {
            $logData = [
                'user_id' => $userId,
                'old_refresh_token' => substr($oldRefreshToken, 0, 10) . '...',
                'new_refresh_token' => substr($newRefreshToken, 0, 10) . '...',
                'ip_address' => request()->ip(),
                'user_agent' => request()->userAgent(),
                'refresh_time' => date('Y-m-d H:i:s'),
                'created_at' => date('Y-m-d H:i:s')
            ];

            // 记录到Laravel日志
            Log::info('Token refresh', $logData);

        } catch (\Exception $e) {
            Log::error('Failed to log token refresh', [
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);
        }
    }

}
