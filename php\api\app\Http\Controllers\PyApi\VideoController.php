<?php

namespace App\Http\Controllers\PyApi;

use App\Enums\ApiCodeEnum;
use App\Http\Controllers\Controller;
use App\Services\AuthService;
use App\Services\PyApi\VideoService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use App\Helpers\LogCheckHelper;

/**
 * AI视频生成与结果管理
 */
class VideoController extends Controller
{
    protected $videoService;

    public function __construct(VideoService $videoService)
    {
        $this->videoService = $videoService;
    }

    /**
     * @ApiTitle(视频生成)
     * @ApiSummary(使用AI生成视频)
     * @ApiMethod(POST)
     * @ApiRoute(/py-api/videos/generate)
     * @ApiHeaders(name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     * @ApiParams(name="prompt", type="string", required=true, description="视频生成提示词")
     * @ApiParams(name="duration", type="int", required=false, description="视频时长（秒）")
     * @ApiParams(name="aspect_ratio", type="string", required=false, description="宽高比：16:9/9:16/1:1")
     * @ApiParams(name="quality", type="string", required=false, description="视频质量：standard/hd/4k")
     * @ApiParams(name="fps", type="int", required=false, description="帧率：24/30/60")
     * @ApiParams(name="style", type="string", required=false, description="视频风格")
     * @ApiParams(name="project_id", type="int", required=false, description="关联项目ID")
     * @ApiParams(name="platform", type="string", required=false, description="指定AI平台：kling/minimax（可选，不指定时使用智能推荐）")
     * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="数据")
     * @ApiReturnParams (name="data.task_id", type="int", required=true, description="任务ID")
     * @ApiReturnParams (name="data.status", type="string", required=true, description="任务状态")
     * @ApiReturnParams (name="data.platform", type="string", required=true, description="实际使用的AI平台")
     * @ApiReturnParams (name="data.user_selected_platform", type="boolean", required=true, description="是否用户主动选择平台")
     * @ApiReturnParams (name="data.estimated_cost", type="decimal", required=true, description="预估成本")
     * @ApiReturnParams (name="data.estimated_duration", type="int", required=false, description="预估生成时间（秒）")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "视频生成任务创建成功",
     *   "data": {
     *     "task_id": 123,
     *     "status": "pending",
     *     "platform": "kling",
     *     "user_selected_platform": true,
     *     "estimated_cost": "0.5000",
     *     "estimated_duration": 180
     *   }
     * })
     */
    public function generate(Request $request)
    {
        try {
            // 🚨 升级：获取支持的平台列表进行动态验证
            $supportedPlatforms = \App\Services\AiServiceClient::getSupportedPlatforms();
            $videoGenerationPlatforms = [];
            foreach ($supportedPlatforms as $platform) {
                $platformConfig = config("ai.platforms.{$platform}");
                if ($platformConfig && in_array('video_generation', $platformConfig['supports'] ?? [])) {
                    $videoGenerationPlatforms[] = $platform;
                }
            }

            $rules = [
                'prompt' => 'required|string|min:5|max:2000',
                'duration' => 'sometimes|integer|min:1|max:60',
                'aspect_ratio' => 'sometimes|string|in:16:9,9:16,1:1,4:3,3:4',
                'quality' => 'sometimes|string|in:standard,hd,4k',
                'fps' => 'sometimes|integer|in:24,30,60',
                'style' => 'sometimes|string|max:100',
                'project_id' => 'sometimes|integer|exists:projects,id',
                'platform' => 'sometimes|string|in:' . implode(',', $videoGenerationPlatforms)
            ];

            $messages = [
                'prompt.required' => '视频生成提示词不能为空',
                'prompt.min' => '视频生成提示词至少5个字符',
                'prompt.max' => '视频生成提示词不能超过2000个字符',
                'duration.min' => '视频时长至少1秒',
                'duration.max' => '视频时长不能超过60秒',
                'aspect_ratio.in' => '宽高比必须是：16:9、9:16、1:1、4:3、3:4之一',
                'quality.in' => '视频质量必须是：standard、hd、4k之一',
                'fps.in' => '帧率必须是：24、30、60之一',
                'project_id.exists' => '项目不存在',
                'platform.in' => 'AI平台必须是：' . implode('、', $videoGenerationPlatforms) . '之一'
            ];

            $this->validateData($request->all(), $rules, $messages, []);

            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
            }

            $user = $authResult['user'];

            $generationParams = [
                'duration' => $request->get('duration', 5),
                'aspect_ratio' => $request->get('aspect_ratio', '16:9'),
                'quality' => $request->get('quality', 'standard'),
                'fps' => $request->get('fps', 30),
                'style' => $request->get('style'),
                'platform' => $request->get('platform') // 🚨 升级：移除默认值，让服务层处理平台选择
            ];

            $result = $this->videoService->generateVideo(
                $user->id,
                $request->prompt,
                $request->project_id,
                $generationParams
            );

            if ($result['success']) {
                return $this->successResponse($result['data'], $result['message'] ?? '视频生成任务创建成功');
            } else {
                return $this->errorResponse($result['code'] ?? ApiCodeEnum::INTERNAL_ERROR, $result['message'] ?? '视频生成失败');
            }
        } catch (\Exception $e) {
            Log::error('视频生成失败', [
                'method' => __METHOD__,
                'user_id' => $user->id ?? null,
                'error_context' => LogCheckHelper::sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '视频生成失败');
        }
    }

    /**
     * @ApiTitle(视频生成状态查询)
     * @ApiSummary(查询视频生成任务的状态和结果)
     * @ApiMethod(GET)
     * @ApiRoute(/py-api/videos/{id}/status)
     * @ApiHeaders(name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     * @ApiParams(name="id", type="int", required=true, description="任务ID")
     * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="任务数据")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "success",
     *   "data": {
     *     "id": 123,
     *     "task_type": "video_generation",
     *     "status": "completed",
     *     "platform": "kling",
     *     "video_url": "https://aiapi.tiptop.cn/videos/generated/123.mp4",
     *     "thumbnail_url": "https://aiapi.tiptop.cn/videos/thumbnails/123.jpg",
     *     "duration": 5,
     *     "resolution": "1920x1080",
     *     "file_size": "15.2MB",
     *     "cost": "0.5000",
     *     "processing_time_ms": 180000,
     *     "created_at": "2024-01-01 12:00:00",
     *     "completed_at": "2024-01-01 12:03:00"
     *   }
     * })
     */
    public function getStatus(Request $request, $id)
    {
        try {
            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
            }

            $user = $authResult['user'];
            $result = $this->videoService->getVideoStatus($id, $user->id);

            if ($result['success']) {
                return $this->successResponse($result['data'], $result['message'] ?? '获取视频状态成功');
            } else {
                return $this->errorResponse($result['code'] ?? ApiCodeEnum::INTERNAL_ERROR, $result['message'] ?? '获取视频状态失败');
            }
        } catch (\Exception $e) {
            Log::error('获取视频状态失败', [
                'method' => __METHOD__,
                'user_id' => $user->id ?? null,
                'error_context' => LogCheckHelper::sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '获取视频状态失败');
        }
    }

    /**
     * @ApiTitle(视频生成结果获取)
     * @ApiSummary(获取视频生成的详细结果和下载信息)
     * @ApiMethod(GET)
     * @ApiRoute(/py-api/videos/{id}/result)
     * @ApiHeaders(name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     * @ApiParams(name="id", type="int", required=true, description="任务ID")
     * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="结果数据")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "success",
     *   "data": {
     *     "task_id": 123,
     *     "video_url": "https://aiapi.tiptop.cn/videos/generated/123.mp4",
     *     "thumbnail_url": "https://aiapi.tiptop.cn/videos/thumbnails/123.jpg",
     *     "preview_url": "https://aiapi.tiptop.cn/videos/previews/123.gif",
     *     "metadata": {
     *       "duration": 5,
     *       "resolution": "1920x1080",
     *       "fps": 30,
     *       "format": "mp4",
     *       "file_size": "15.2MB",
     *       "bitrate": "2000kbps"
     *     },
     *     "download_info": {
     *       "direct_url": "https://aiapi.tiptop.cn/videos/generated/123.mp4",
     *       "expires_at": "2024-01-08 12:00:00"
     *     }
     *   }
     * })
     */
    public function getResult(Request $request, $id)
    {
        try {
            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
            }

            $user = $authResult['user'];
            $result = $this->videoService->getVideoResult($id, $user->id);

            if ($result['success']) {
                return $this->successResponse($result['data'], $result['message'] ?? '获取视频结果成功');
            } else {
                return $this->errorResponse($result['code'] ?? ApiCodeEnum::INTERNAL_ERROR, $result['message'] ?? '获取视频结果失败');
            }
        } catch (\Exception $e) {
            Log::error('获取视频结果失败', [
                'method' => __METHOD__,
                'user_id' => $user->id ?? null,
                'error_context' => LogCheckHelper::sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '获取视频结果失败');
        }
    }

    /**
     * @ApiTitle(获取视频生成平台选项)
     * @ApiSummary(获取可用的视频生成AI平台选项列表)
     * @ApiMethod(GET)
     * @ApiRoute(/py-api/ai-models/platform-options/video_generation)
     * @ApiHeaders(name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="平台选项数据")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "success",
     *   "data": {
     *     "task_type": "video_generation",
     *     "platforms": [
     *       {
     *         "platform_key": "kling",
     *         "name": "KlingAI",
     *         "description": "视频生成领导者",
     *         "pricing": {"cost_per_request": 0.0005, "currency": "CNY"},
     *         "performance": {"estimated_time": "2-5分钟"},
     *         "features": ["视频流畅", "画面清晰", "动作自然"],
     *         "availability": {"status": "available", "mode": "mock"}
     *       }
     *     ],
     *     "total_count": 2
     *   }
     * })
     */
    public function getPlatformOptions(Request $request)
    {
        try {
            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
            }

            $user = $authResult['user'];
            $taskType = 'video_generation';

            // 获取平台选项
            $result = \App\Services\AiServiceClient::getPlatformOptions($taskType, $user->id);

            if ($result['success']) {
                return $this->successResponse($result['data'], 'success');
            } else {
                return $this->errorResponse(ApiCodeEnum::MY_SERVICE_ERROR, $result['error'], $result['data']);
            }
        } catch (\Exception $e) {
            Log::error('获取视频生成平台选项失败', [
                'method' => __METHOD__,
                'user_id' => $user->id ?? null,
                'error_context' => LogCheckHelper::sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '获取平台选项失败');
        }
    }

    /**
     * @ApiTitle(获取视频生成推荐平台)
     * @ApiSummary(获取基于用户偏好的视频生成AI平台推荐)
     * @ApiMethod(GET)
     * @ApiRoute(/py-api/ai-models/user-recommendations/video_generation)
     * @ApiHeaders(name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     * @ApiParams(name="limit", type="int", required=false, description="推荐数量限制，默认3个")
     * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="推荐数据")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "success",
     *   "data": {
     *     "task_type": "video_generation",
     *     "recommendations": [
     *       {
     *         "platform_key": "kling",
     *         "name": "KlingAI",
     *         "recommendation_reason": "您的首选平台，视频质量优秀",
     *         "personalization": {
     *           "is_preferred": true,
     *           "usage_count": 3,
     *           "recommendation_score": 0.90
     *         }
     *       }
     *     ],
     *     "total_available": 2
     *   }
     * })
     */
    public function getUserRecommendations(Request $request)
    {
        try {
            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
            }

            $user = $authResult['user'];
            $taskType = 'video_generation';
            $limit = (int) $request->get('limit', 3);

            // 获取用户推荐
            $result = \App\Services\AiServiceClient::getUserRecommendations($user->id, $taskType, $limit);

            if ($result['success']) {
                return $this->successResponse($result['data'], 'success');
            } else {
                return $this->errorResponse(ApiCodeEnum::MY_SERVICE_ERROR, $result['error'], $result['data']);
            }
        } catch (\Exception $e) {
            Log::error('获取视频生成推荐平台失败', [
                'method' => __METHOD__,
                'user_id' => $user->id ?? null,
                'error_context' => LogCheckHelper::sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '获取推荐平台失败');
        }
    }
}
