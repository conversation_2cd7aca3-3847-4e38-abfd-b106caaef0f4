<?php
/**
 * 环境切换机制使用示例
 * 
 * 🚨 架构边界规范：环境切换机制实现示例
 * ✅ 本地开发：调用模拟服务（无真实费用）
 * ✅ 生产环境：调用真实平台（产生真实费用）
 * 
 * 环境切换通过环境变量控制：
 * - AI_SERVICE_MODE=mock (开发环境)
 * - AI_SERVICE_MODE=real (生产环境)
 * - THIRD_PARTY_MODE=mock (开发环境)
 * - THIRD_PARTY_MODE=real (生产环境)
 */

require_once __DIR__ . '/../vendor/autoload.php';

use App\Services\PyApi\AiServiceClient;
use App\Services\PyApi\ThirdPartyServiceClient;

echo "🚨 环境切换机制使用示例\n";
echo str_repeat("=", 50) . "\n\n";

// ==================== AI服务调用示例 ====================
echo "📱 AI服务调用示例\n";
echo str_repeat("-", 30) . "\n";

// 检查当前AI服务模式
$aiMode = AiServiceClient::getServiceMode();
$isAiMock = AiServiceClient::isMockMode();

echo "当前AI服务模式: {$aiMode}\n";
echo "是否为模拟模式: " . ($isAiMock ? '是' : '否') . "\n\n";

// 1. DeepSeek文本生成示例
echo "1. 调用DeepSeek服务（剧情生成）\n";
try {
    $response = AiServiceClient::call('deepseek', [
        'model' => 'deepseek-chat',
        'messages' => [
            ['role' => 'user', 'content' => '请生成一个科幻故事剧情']
        ],
        'max_tokens' => 1000
    ]);
    
    if ($response['success']) {
        echo "✅ 调用成功 (模式: {$response['mode']})\n";
        echo "平台: {$response['platform']}\n";
        if ($response['mode'] === 'mock') {
            echo "🔍 模拟响应数据: " . substr(json_encode($response['data']), 0, 100) . "...\n";
        }
    } else {
        echo "❌ 调用失败: {$response['error']}\n";
    }
} catch (Exception $e) {
    echo "❌ 异常: " . $e->getMessage() . "\n";
}
echo "\n";

// 2. LiblibAI图像生成示例
echo "2. 调用LiblibAI服务（图像生成）\n";
try {
    $response = AiServiceClient::call('liblib', [
        'prompt' => '一个美丽的科幻城市，未来感，高清',
        'width' => 1024,
        'height' => 1024,
        'model' => 'star-3-alpha'
    ]);
    
    if ($response['success']) {
        echo "✅ 调用成功 (模式: {$response['mode']})\n";
        echo "平台: {$response['platform']}\n";
        if ($response['mode'] === 'mock') {
            echo "🔍 模拟图像URL: " . ($response['data']['image_url'] ?? '模拟图像地址') . "\n";
        }
    } else {
        echo "❌ 调用失败: {$response['error']}\n";
    }
} catch (Exception $e) {
    echo "❌ 异常: " . $e->getMessage() . "\n";
}
echo "\n";

// ==================== 第三方服务调用示例 ====================
echo "🔗 第三方服务调用示例\n";
echo str_repeat("-", 30) . "\n";

// 检查当前第三方服务模式
$thirdPartyMode = ThirdPartyServiceClient::getServiceMode();
$isThirdPartyMock = ThirdPartyServiceClient::isMockMode();

echo "当前第三方服务模式: {$thirdPartyMode}\n";
echo "是否为模拟模式: " . ($isThirdPartyMock ? '是' : '否') . "\n\n";

// 1. 微信OAuth示例
echo "1. 调用微信OAuth服务\n";
try {
    $response = ThirdPartyServiceClient::call('wechat', [
        'action' => 'oauth_authorize',
        'appid' => 'mock_app_id',
        'redirect_uri' => 'https://yourdomain.com/callback',
        'response_type' => 'code',
        'scope' => 'snsapi_userinfo'
    ]);
    
    if ($response['success']) {
        echo "✅ 调用成功 (模式: {$response['mode']})\n";
        echo "平台: {$response['platform']}\n";
        if ($response['mode'] === 'mock') {
            echo "🔍 模拟授权URL: " . ($response['data']['authorize_url'] ?? '模拟授权地址') . "\n";
        }
    } else {
        echo "❌ 调用失败: {$response['error']}\n";
    }
} catch (Exception $e) {
    echo "❌ 异常: " . $e->getMessage() . "\n";
}
echo "\n";

// 2. 支付宝支付示例
echo "2. 调用支付宝支付服务\n";
try {
    $response = ThirdPartyServiceClient::call('alipay', [
        'action' => 'trade_page_pay',
        'out_trade_no' => 'ORDER_' . time(),
        'total_amount' => '0.01',
        'subject' => '测试商品',
        'return_url' => 'https://yourdomain.com/return',
        'notify_url' => 'https://yourdomain.com/notify'
    ]);
    
    if ($response['success']) {
        echo "✅ 调用成功 (模式: {$response['mode']})\n";
        echo "平台: {$response['platform']}\n";
        if ($response['mode'] === 'mock') {
            echo "🔍 模拟支付URL: " . ($response['data']['pay_url'] ?? '模拟支付地址') . "\n";
        }
    } else {
        echo "❌ 调用失败: {$response['error']}\n";
    }
} catch (Exception $e) {
    echo "❌ 异常: " . $e->getMessage() . "\n";
}
echo "\n";

// ==================== 配置验证示例 ====================
echo "🔍 配置验证示例\n";
echo str_repeat("-", 30) . "\n";

// 验证AI平台配置
$aiPlatforms = AiServiceClient::getSupportedPlatforms();
echo "支持的AI平台: " . implode(', ', $aiPlatforms) . "\n";

foreach ($aiPlatforms as $platform) {
    $validation = AiServiceClient::validatePlatformConfig($platform);
    $status = $validation['valid'] ? '✅' : '❌';
    echo "{$status} {$platform}: " . ($validation['valid'] ? "配置正确 (模式: {$validation['mode']})" : $validation['error']) . "\n";
}
echo "\n";

// 验证第三方平台配置
$thirdPartyPlatforms = ThirdPartyServiceClient::getSupportedPlatforms();
echo "支持的第三方平台: " . implode(', ', $thirdPartyPlatforms) . "\n";

foreach ($thirdPartyPlatforms as $platform) {
    $validation = ThirdPartyServiceClient::validatePlatformConfig($platform);
    $status = $validation['valid'] ? '✅' : '❌';
    echo "{$status} {$platform}: " . ($validation['valid'] ? "配置正确 (模式: {$validation['mode']})" : $validation['error']) . "\n";
}
echo "\n";

// ==================== 环境切换说明 ====================
echo "📋 环境切换说明\n";
echo str_repeat("-", 30) . "\n";
echo "🔧 开发环境配置 (.env):\n";
echo "AI_SERVICE_MODE=mock\n";
echo "THIRD_PARTY_MODE=mock\n";
echo "AI_MOCK_URL=https://aiapi.tiptop.cn\n";
echo "THIRD_PARTY_MOCK_URL=https://thirdapi.tiptop.cn\n\n";

echo "🚀 生产环境配置 (.env):\n";
echo "AI_SERVICE_MODE=real\n";
echo "THIRD_PARTY_MODE=real\n";
echo "DEEPSEEK_API_KEY=your_real_api_key\n";
echo "WECHAT_APP_ID=your_real_app_id\n";
echo "ALIPAY_APP_ID=your_real_app_id\n\n";

echo "⚠️ 重要提醒:\n";
echo "- 模拟模式 (mock): 不产生真实费用，不执行真实业务操作\n";
echo "- 真实模式 (real): 产生真实费用，执行真实业务操作\n";
echo "- 切换环境前请确保相关配置正确\n";
echo "- 生产环境切换需要谨慎操作\n\n";

echo "✅ 示例执行完成\n";
