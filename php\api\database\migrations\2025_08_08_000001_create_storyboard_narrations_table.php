<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

/**
 * 创建分镜旁白/解说表
 * 
 * 🚨 架构边界规范：
 * ✅ 独立表设计（倾向B），支持历史版本与扩展
 * ✅ 外键约束：storyboard_id → project_storyboards.id ON DELETE CASCADE
 * ✅ 外键约束：narrator_character_id → project_characters.id ON DELETE SET NULL
 * ✅ 支持 language/emotion/speed/pitch 可选参数
 * ✅ 支持 last_preview_url 便于前端复用
 */
class CreateStoryboardNarrationsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('p_storyboard_narrations', function (Blueprint $table) {
            $table->bigIncrements('id');
            
            // 关联字段
            $table->unsignedBigInteger('storyboard_id')->comment('分镜ID');
            $table->unsignedBigInteger('narrator_character_id')->nullable()->comment('解说角色ID（项目角色）');
            
            // 音色配置
            $table->string('voice_id', 100)->nullable()->comment('音色ID');
            $table->string('platform', 50)->default('volcengine')->comment('AI平台');
            $table->string('language', 10)->default('zh-CN')->comment('语言');
            $table->string('emotion', 50)->default('neutral')->comment('情绪');
            $table->decimal('speed', 3, 1)->default(1.0)->comment('语速（0.5-2.0）');
            $table->decimal('pitch', 3, 1)->default(1.0)->comment('音调（0.5-2.0）');
            
            // 文本与状态
            $table->text('subtitle_text')->nullable()->comment('旁白文本（可选，默认沿用分镜）');
            $table->enum('status', ['active', 'inactive'])->default('active')->comment('状态');
            
            // 试听相关
            $table->string('last_preview_url', 500)->nullable()->comment('最后试听URL（便于前端复用）');
            
            $table->timestamps();
            
            // 索引
            $table->index('storyboard_id');
            $table->index('narrator_character_id');
            $table->index(['voice_id', 'platform']);
            
            // 外键约束
            $table->foreign('storyboard_id')
                  ->references('id')
                  ->on('p_project_storyboards')
                  ->onDelete('cascade');
                  
            $table->foreign('narrator_character_id')
                  ->references('id')
                  ->on('p_project_characters')
                  ->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('p_storyboard_narrations');
    }
}
