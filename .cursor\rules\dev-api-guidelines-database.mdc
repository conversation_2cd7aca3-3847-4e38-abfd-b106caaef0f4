---
description: 数据库设计规范文档
globs: ["php/api/**/*.php", "php/api/**/*.json", "php/api/**/.env*", "php/api/**/routes/**", "php/api/**/config/**"]
alwaysApply: true
---

# 📊 数据库设计规范文档

## 📋 数据库设计规范说明

### 🔧 表前缀规范
- **配置前缀**: 数据库配置中设置 `DB_PREFIX=p_`
- **实际表名**: 迁移文件中的表名会自动添加 `p_` 前缀
- **示例**: 迁移文件中的 `users` 表，实际数据库中为 `p_users`

### 📝 字段类型规范
- **主键**: `BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY`
- **外键**: `BIGINT UNSIGNED` (关联其他表的ID)
- **字符串**: `VARCHAR(长度)` (指定具体长度)
- **长文本**: `TEXT` (不限长度的文本内容)
- **JSON数据**: `JSON` (结构化数据存储)
- **布尔值**: `BOOLEAN` (TRUE/FALSE)
- **整数**: `INT` (普通整数) / `BIGINT` (大整数)
- **小数**: `DECIMAL(总位数,小数位数)` (精确小数)
- **时间戳**: `TIMESTAMP` (带时区的时间)
- **枚举**: `ENUM('值1','值2','值3')` (限定值选择)

### 🎯 命名约定
- **表名**: 小写字母 + 下划线分隔 (如: `user_achievements`)
- **字段名**: 小写字母 + 下划线分隔 (如: `created_at`)
- **外键**: 关联表名 + `_id` (如: `user_id`)
- **布尔字段**: `is_` 开头 (如: `is_active`)
- **计数字段**: `_count` 结尾 (如: `view_count`)

### ⚡ 索引规范
- **主键索引**: 自动创建
- **唯一索引**: 唯一约束字段 (如: `username`, `email`)
- **外键索引**: 关联字段自动创建
- **复合索引**: 多字段组合查询 (如: `(user_id, status)`)
- **功能索引**: 特定业务查询优化

## 核心数据表结构

### 基础业务表
- **p_users**: 用户表（用户信息、认证、偏好设置）
  - `id` - BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY - 主键ID
  - `username` - VARCHAR(100) UNIQUE NOT NULL - 用户名，唯一标识
  - `email` - VARCHAR(255) UNIQUE NOT NULL - 邮箱地址
  - `password` - VARCHAR(255) NOT NULL - 加密密码
  - `nickname` - VARCHAR(100) NULL - 用户昵称
  - `avatar` - VARCHAR(500) NULL - 头像URL
  - `bio` - TEXT NULL - 个人简介
  - `level` - INT DEFAULT 1 - 用户等级
  - `experience` - INT DEFAULT 0 - 经验值
  - `follower_count` - INT DEFAULT 0 - 粉丝数量
  - `following_count` - INT DEFAULT 0 - 关注数量
  - `inviter_id` - BIGINT UNSIGNED NULL - 邀请人ID，关联p_users表
  - `remark` - VARCHAR(500) NULL - 备注信息
  - `status` - ENUM('active','inactive','banned') DEFAULT 'active' - 账户状态
  - `points` - DECIMAL(10,2) DEFAULT 0.00 - 积分余额
  - `frozen_points` - DECIMAL(10,2) DEFAULT 0.00 - 冻结积分
  - `is_vip` - BOOLEAN DEFAULT FALSE - 是否VIP用户
  - `vip_expires_at` - TIMESTAMP NULL - VIP过期时间
  - `last_login_ip` - VARCHAR(45) NULL - 最后登录IP
  - `last_login_at` - TIMESTAMP NULL - 最后登录时间
  - `created_at` - TIMESTAMP DEFAULT CURRENT_TIMESTAMP - 创建时间
  - `updated_at` - TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP - 更新时间

- **p_points_transactions**: 积分交易表（积分流水、冻结、返还）
  - `id` - BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY - 主键ID
  - `user_id` - BIGINT UNSIGNED NOT NULL - 用户ID，关联p_users表
  - `business_type` - VARCHAR(50) NOT NULL - 业务类型（ai_generation/recharge/refund/reward）
  - `business_id` - VARCHAR(100) NULL - 业务ID（关联具体业务记录）
  - `amount` - DECIMAL(10,2) NOT NULL - 交易金额
  - `status` - ENUM('pending','completed','failed','cancelled') DEFAULT 'pending' - 交易状态
  - `ai_platform` - VARCHAR(50) NULL - AI平台标识
  - `request_data` - JSON NULL - 请求数据
  - `response_data` - JSON NULL - 响应数据
  - `timeout_seconds` - INT NULL - 超时时间（秒）
  - `completed_at` - TIMESTAMP NULL - 完成时间
  - `failure_reason` - TEXT NULL - 失败原因
  - `created_at` - TIMESTAMP DEFAULT CURRENT_TIMESTAMP - 创建时间
  - `updated_at` - TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP - 更新时间

- **p_points_freeze**: 积分冻结表（冻结机制、安全保障）
  - `id` - BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY - 主键ID
  - `user_id` - BIGINT UNSIGNED NOT NULL - 用户ID，关联p_users表
  - `amount` - DECIMAL(10,2) NOT NULL - 冻结金额
  - `business_type` - VARCHAR(50) NOT NULL - 业务类型（ai_generation/purchase/withdrawal）
  - `business_id` - VARCHAR(100) NULL - 业务ID（关联具体业务记录）
  - `reason` - VARCHAR(255) NULL - 冻结原因
  - `status` - ENUM('frozen','released','expired') DEFAULT 'frozen' - 冻结状态
  - `released_at` - TIMESTAMP NULL - 释放时间
  - `expires_at` - TIMESTAMP NULL - 过期时间
  - `metadata` - JSON NULL - 冻结元数据
  - `created_at` - TIMESTAMP DEFAULT CURRENT_TIMESTAMP - 创建时间
  - `updated_at` - TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP - 更新时间

### AI生成相关表
- **p_ai_model_configs**: AI模型配置表（AI平台配置、模型管理）
  - `id` - BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY - 主键ID
  - `platform` - VARCHAR(50) NOT NULL - AI平台标识（deepseek/liblib/kling/minimax/volcengine）
  - `model_name` - VARCHAR(100) NOT NULL - 模型名称
  - `model_type` - VARCHAR(50) NOT NULL - 模型类型（text/image/video/voice/music/sound）
  - `api_endpoint` - VARCHAR(500) NOT NULL - API端点URL
  - `config_params` - JSON NOT NULL - 配置参数
  - `capabilities` - JSON NOT NULL - 模型能力描述
  - `is_active` - BOOLEAN DEFAULT TRUE - 是否激活
  - `is_default` - BOOLEAN DEFAULT FALSE - 是否默认模型
  - `priority` - INT DEFAULT 0 - 优先级排序
  - `cost_per_request` - DECIMAL(8,4) DEFAULT 0.0000 - 每次请求成本
  - `max_tokens` - INT DEFAULT 4000 - 最大令牌数
  - `timeout_seconds` - INT DEFAULT 30 - 超时时间（秒）
  - `rate_limits` - JSON NULL - 速率限制配置
  - `performance_metrics` - JSON NULL - 性能指标
  - `last_health_check` - TIMESTAMP NULL - 最后健康检查时间
  - `health_status` - ENUM('healthy','degraded','unhealthy','unknown') DEFAULT 'unknown' - 健康状态
  - `health_message` - TEXT NULL - 健康状态消息
  - `deleted_at` - TIMESTAMP NULL - 软删除时间
  - `created_at` - TIMESTAMP DEFAULT CURRENT_TIMESTAMP - 创建时间
  - `updated_at` - TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP - 更新时间

- **p_style_library**: 风格库表（剧情风格管理、AI生成配置）
  - `id` - BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY - 主键ID
  - `name` - VARCHAR(100) NOT NULL - 风格名称
  - `description` - TEXT NULL - 风格描述
  - `category` - VARCHAR(50) NULL - 风格分类
  - `style_params` - JSON NULL - 风格参数
  - `preview_image` - VARCHAR(500) NULL - 预览图片URL
  - `is_active` - BOOLEAN DEFAULT TRUE - 是否激活
  - `sort_order` - INT DEFAULT 0 - 排序权重
  - `usage_count` - INT DEFAULT 0 - 使用次数
  - `created_at` - TIMESTAMP DEFAULT CURRENT_TIMESTAMP - 创建时间
  - `updated_at` - TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP - 更新时间

- **p_character_library**: 角色库表（AI生成角色信息、特征描述）
  - `id` - BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY - 主键ID
  - `name` - VARCHAR(100) NOT NULL - 角色名称
  - `description` - TEXT NULL - 角色描述
  - `category_id` - BIGINT UNSIGNED NULL - 角色分类ID，关联p_character_categories表
  - `gender` - ENUM('male','female','other') NULL - 性别
  - `age_range` - VARCHAR(50) NULL - 年龄范围
  - `personality_traits` - JSON NULL - 性格特征数组
  - `appearance_description` - TEXT NULL - 外貌描述
  - `voice_characteristics` - TEXT NULL - 声音特征
  - `background_story` - TEXT NULL - 背景故事
  - `avatar_url` - VARCHAR(500) NULL - 角色头像URL
  - `model_params` - JSON NULL - 模型参数
  - `is_active` - BOOLEAN DEFAULT TRUE - 是否激活
  - `is_premium` - BOOLEAN DEFAULT FALSE - 是否高级角色
  - `usage_count` - INT DEFAULT 0 - 使用次数
  - `rating` - DECIMAL(3,2) DEFAULT 0.00 - 用户评分（0-5）
  - `created_at` - TIMESTAMP DEFAULT CURRENT_TIMESTAMP - 创建时间
  - `updated_at` - TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP - 更新时间

- **p_character_categories**: 角色分类表（角色分类管理）
  - `id` - BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY - 主键ID
  - `name` - VARCHAR(100) NOT NULL - 分类名称
  - `description` - TEXT NULL - 分类描述
  - `icon` - VARCHAR(255) NULL - 分类图标
  - `sort_order` - INT DEFAULT 0 - 排序权重
  - `is_active` - BOOLEAN DEFAULT TRUE - 是否激活
  - `created_at` - TIMESTAMP DEFAULT CURRENT_TIMESTAMP - 创建时间
  - `updated_at` - TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP - 更新时间

- **p_music_library**: 音乐库表（AI生成音乐存储、MiniMax平台）
  - `id` - BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY - 主键ID
  - `name` - VARCHAR(255) NOT NULL - 音乐名称
  - `description` - TEXT NULL - 音乐描述
  - `category` - VARCHAR(50) NULL - 音乐分类（背景音乐/主题音乐/环境音效）
  - `genre` - VARCHAR(50) NULL - 音乐风格（古典/流行/电子/民族等）
  - `mood` - VARCHAR(50) NULL - 音乐情绪（欢快/悲伤/紧张/轻松等）
  - `tempo` - VARCHAR(20) NULL - 节拍（慢板/中板/快板）
  - `duration` - INT NULL - 时长（秒）
  - `file_url` - VARCHAR(1000) NULL - 音乐文件URL
  - `file_size` - BIGINT UNSIGNED DEFAULT 0 - 文件大小（字节）
  - `file_format` - VARCHAR(20) NULL - 文件格式（mp3/wav/flac）
  - `sample_rate` - INT NULL - 采样率（Hz）
  - `bit_rate` - INT NULL - 比特率（kbps）
  - `ai_platform` - VARCHAR(50) NULL - AI生成平台（minimax等）
  - `generation_params` - JSON NULL - 生成参数
  - `prompt_text` - TEXT NULL - 生成提示词
  - `tags` - JSON NULL - 音乐标签数组
  - `is_loop` - BOOLEAN DEFAULT FALSE - 是否循环播放
  - `is_premium` - BOOLEAN DEFAULT FALSE - 是否高级音乐
  - `usage_count` - INT DEFAULT 0 - 使用次数
  - `rating` - DECIMAL(3,2) DEFAULT 0.00 - 用户评分（0-5）
  - `is_active` - BOOLEAN DEFAULT TRUE - 是否激活
  - `created_at` - TIMESTAMP DEFAULT CURRENT_TIMESTAMP - 创建时间
  - `updated_at` - TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP - 更新时间

- **p_sound_library**: 音效库表（AI生成音效存储、火山引擎豆包平台）
  - `id` - BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY - 主键ID
  - `name` - VARCHAR(255) NOT NULL - 音效名称
  - `description` - TEXT NULL - 音效描述
  - `category` - VARCHAR(50) NULL - 音效分类（环境音/动作音/情绪音/特效音）
  - `sound_type` - VARCHAR(50) NULL - 音效类型（自然/机械/人声/动物等）
  - `scene` - VARCHAR(50) NULL - 适用场景（室内/室外/战斗/对话等）
  - `intensity` - VARCHAR(20) NULL - 强度等级（轻微/中等/强烈）
  - `duration` - INT NULL - 时长（秒）
  - `file_url` - VARCHAR(1000) NULL - 音效文件URL
  - `file_size` - BIGINT UNSIGNED DEFAULT 0 - 文件大小（字节）
  - `file_format` - VARCHAR(20) NULL - 文件格式（mp3/wav/ogg）
  - `sample_rate` - INT NULL - 采样率（Hz）
  - `bit_rate` - INT NULL - 比特率（kbps）
  - `ai_platform` - VARCHAR(50) NULL - AI生成平台（volcengine等）
  - `generation_params` - JSON NULL - 生成参数
  - `prompt_text` - TEXT NULL - 生成提示词
  - `tags` - JSON NULL - 音效标签数组
  - `is_loop` - BOOLEAN DEFAULT FALSE - 是否循环播放
  - `is_premium` - BOOLEAN DEFAULT FALSE - 是否高级音效
  - `usage_count` - INT DEFAULT 0 - 使用次数
  - `rating` - DECIMAL(3,2) DEFAULT 0.00 - 用户评分（0-5）
  - `is_active` - BOOLEAN DEFAULT TRUE - 是否激活
  - `created_at` - TIMESTAMP DEFAULT CURRENT_TIMESTAMP - 创建时间
  - `updated_at` - TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP - 更新时间

- **p_timbre_library**: 音色库表（AI生成音色存储、双平台支持）
  - `id` - BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY - 主键ID
  - `name` - VARCHAR(255) NOT NULL - 音色名称
  - `description` - TEXT NULL - 音色描述
  - `voice_type` - VARCHAR(50) NULL - 声音类型（男声/女声/童声/老年声）
  - `language` - VARCHAR(20) NULL - 语言（中文/英文/日文等）
  - `accent` - VARCHAR(50) NULL - 口音（标准/方言/地区口音）
  - `emotion` - VARCHAR(50) NULL - 情感色彩（平静/激动/温柔/严肃等）
  - `age_range` - VARCHAR(20) NULL - 年龄范围（儿童/青年/中年/老年）
  - `pitch_range` - VARCHAR(50) NULL - 音调范围（低音/中音/高音）
  - `speed_range` - VARCHAR(50) NULL - 语速范围（慢速/正常/快速）
  - `sample_url` - VARCHAR(1000) NULL - 音色样本URL
  - `sample_text` - TEXT NULL - 样本文本
  - `ai_platform` - VARCHAR(50) NULL - AI生成平台（minimax/volcengine等）
  - `model_params` - JSON NULL - 模型参数
  - `voice_id` - VARCHAR(100) NULL - 平台音色ID
  - `quality_level` - VARCHAR(20) NULL - 音质等级（标准/高清/超清）
  - `is_premium` - BOOLEAN DEFAULT FALSE - 是否高级音色
  - `usage_count` - INT DEFAULT 0 - 使用次数
  - `rating` - DECIMAL(3,2) DEFAULT 0.00 - 用户评分（0-5）
  - `is_active` - BOOLEAN DEFAULT TRUE - 是否激活
  - `created_at` - TIMESTAMP DEFAULT CURRENT_TIMESTAMP - 创建时间
  - `updated_at` - TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP - 更新时间

- **p_story_library**: 故事库表（AI生成故事内容、项目表关联）
  - `id` - BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY - 主键ID
  - `project_id` - BIGINT UNSIGNED NULL - 项目ID，关联p_projects表
  - `title` - VARCHAR(255) NOT NULL - 故事标题
  - `summary` - TEXT NULL - 故事摘要
  - `content` - LONGTEXT NOT NULL - 故事内容
  - `genre` - VARCHAR(50) NULL - 故事类型（科幻/奇幻/悬疑/爱情等）
  - `theme` - VARCHAR(100) NULL - 故事主题
  - `target_audience` - VARCHAR(50) NULL - 目标受众（儿童/青少年/成人）
  - `word_count` - INT DEFAULT 0 - 字数统计
  - `chapter_count` - INT DEFAULT 1 - 章节数量
  - `reading_time` - INT NULL - 预计阅读时间（分钟）
  - `difficulty_level` - VARCHAR(20) NULL - 难度等级（简单/中等/困难）
  - `ai_platform` - VARCHAR(50) NULL - AI生成平台
  - `generation_params` - JSON NULL - 生成参数
  - `prompt_text` - TEXT NULL - 生成提示词
  - `characters` - JSON NULL - 角色信息数组
  - `plot_outline` - TEXT NULL - 情节大纲
  - `tags` - JSON NULL - 故事标签数组
  - `status` - ENUM('draft','completed','published','archived') DEFAULT 'draft' - 故事状态
  - `is_public` - BOOLEAN DEFAULT FALSE - 是否公开
  - `view_count` - INT DEFAULT 0 - 查看次数
  - `like_count` - INT DEFAULT 0 - 点赞次数
  - `rating` - DECIMAL(3,2) DEFAULT 0.00 - 用户评分（0-5）
  - `created_at` - TIMESTAMP DEFAULT CURRENT_TIMESTAMP - 创建时间
  - `updated_at` - TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP - 更新时间

- **p_storyboard_action_library**: 分镜动作库表（分镜动作模板、简笔画动作图）
  - `id` - BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY - 主键ID
  - `title` - VARCHAR(255) NOT NULL - 动作标题
  - `description` - TEXT NULL - 动作描述
  - `camera_shot` - ENUM('close_up','medium_shot','long_shot','extreme_close_up','extreme_long_shot','over_shoulder','bird_eye','worm_eye') NOT NULL - 镜头参数（特写/中景/远景/大特写/大远景/过肩镜头/鸟瞰/仰视）
  - `action_type` - VARCHAR(100) NOT NULL - 动作类型（躺着/睡觉/向上看/开门/坐下/走路/跑步/跳跃/挥手/拥抱等）
  - `difficulty_level` - ENUM('easy','medium','hard') DEFAULT 'medium' - 动作难度（简单/中等/困难）
  - `sketch_image_url` - VARCHAR(500) NULL - 简笔画动作图URL
  - `is_user_uploaded` - BOOLEAN DEFAULT FALSE - 是否用户上传（0=否，1=是）
  - `uploaded_by` - BIGINT UNSIGNED NULL - 上传用户ID，关联p_users表
  - `tags` - JSON NULL - 动作标签（JSON数组）
  - `usage_count` - INT DEFAULT 0 - 使用次数
  - `sort_order` - INT DEFAULT 0 - 排序权重
  - `created_at` - TIMESTAMP DEFAULT CURRENT_TIMESTAMP - 创建时间
  - `updated_at` - TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP - 更新时间
  - **索引**:
    - INDEX `idx_camera_shot` (`camera_shot`)
    - INDEX `idx_action_type` (`action_type`)
    - INDEX `idx_difficulty_level` (`difficulty_level`)
    - INDEX `idx_is_user_uploaded` (`is_user_uploaded`)
    - INDEX `idx_uploaded_by` (`uploaded_by`)
    - INDEX `idx_usage_count` (`usage_count`)
    - INDEX `idx_sort_order` (`sort_order`)
  - **外键约束**:
    - FOREIGN KEY (`uploaded_by`) REFERENCES `p_users`(`id`) ON DELETE SET NULL

### 核心资源管理表
- **p_resources**: AI生成资源表（资源管理、模块关联、状态跟踪）
  - `id` - BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY - 主键ID
  - `resource_uuid` - VARCHAR(36) UNIQUE NOT NULL - 资源唯一标识符
  - `user_id` - BIGINT UNSIGNED NOT NULL - 用户ID，关联p_users表
  - `project_id` - BIGINT UNSIGNED NULL - 项目ID，关联p_projects表
  - `module_type` - VARCHAR(50) NOT NULL - 模块类型（story/character/style/image/video/voice/music/sound）
  - `module_id` - BIGINT UNSIGNED NULL - 模块ID（关联具体模块表）
  - `resource_type` - VARCHAR(50) NOT NULL - 资源类型（text/image/video/audio）
  - `resource_name` - VARCHAR(255) NOT NULL - 资源名称
  - `resource_description` - TEXT NULL - 资源描述
  - `file_path` - VARCHAR(1000) NULL - 文件路径
  - `file_size` - BIGINT UNSIGNED DEFAULT 0 - 文件大小（字节）
  - `file_format` - VARCHAR(50) NULL - 文件格式
  - `duration` - INT NULL - 时长（秒，适用于音视频）
  - `dimensions` - JSON NULL - 尺寸信息（适用于图片视频）
  - `metadata` - JSON NULL - 资源元数据
  - `status` - ENUM('generating','completed','failed','deleted') DEFAULT 'generating' - 资源状态
  - `review_status` - ENUM('pending','approved','rejected','auto_approved') DEFAULT 'pending' - 审核状态
  - `is_public` - BOOLEAN DEFAULT FALSE - 是否公开
  - `download_count` - INT DEFAULT 0 - 下载次数
  - `view_count` - INT DEFAULT 0 - 查看次数
  - `downloaded_by_python` - BOOLEAN DEFAULT FALSE - 是否已被Python工具下载
  - `local_path` - VARCHAR(1000) NULL - 本地存储路径
  - `created_at` - TIMESTAMP DEFAULT CURRENT_TIMESTAMP - 创建时间
  - `updated_at` - TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP - 更新时间

- **p_resource_versions**: 资源版本表（版本控制、提示词管理、本地导出）
  - `id` - BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY - 主键ID
  - `resource_id` - BIGINT UNSIGNED NOT NULL - 资源ID，关联p_resources表
  - `version_number` - VARCHAR(20) NOT NULL - 版本号
  - `version_name` - VARCHAR(255) NULL - 版本名称
  - `description` - TEXT NULL - 版本描述
  - `prompt_text` - TEXT NULL - 生成提示词
  - `generation_params` - JSON NULL - 生成参数
  - `resource_url` - VARCHAR(1000) NULL - 资源URL
  - `file_size` - BIGINT UNSIGNED DEFAULT 0 - 文件大小
  - `file_hash` - VARCHAR(64) NULL - 文件哈希值
  - `platform` - VARCHAR(50) NULL - 生成平台
  - `model_name` - VARCHAR(100) NULL - 使用的模型名称
  - `generation_time_ms` - INT NULL - 生成耗时（毫秒）
  - `cost` - DECIMAL(8,4) DEFAULT 0.0000 - 生成成本
  - `quality_score` - DECIMAL(3,2) DEFAULT 0.00 - 质量评分（0-5）
  - `is_current` - BOOLEAN DEFAULT FALSE - 是否当前版本
  - `status` - ENUM('active','archived','deleted') DEFAULT 'active' - 版本状态
  - `created_at` - TIMESTAMP DEFAULT CURRENT_TIMESTAMP - 创建时间
  - `updated_at` - TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP - 更新时间

### 任务管理表
- **p_ai_generation_tasks**: AI生成任务表（任务状态、进度、结果）
  - `id` - BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY - 主键ID
  - `user_id` - BIGINT UNSIGNED NOT NULL - 用户ID，关联p_users表
  - `project_id` - BIGINT UNSIGNED NULL - 项目ID，关联p_projects表
  - `model_config_id` - BIGINT UNSIGNED NULL - 模型配置ID，关联p_ai_model_configs表
  - `task_type` - VARCHAR(50) NOT NULL - 任务类型（text/image/video/voice/music/sound）
  - `platform` - VARCHAR(50) NOT NULL - AI平台标识
  - `model_name` - VARCHAR(100) NOT NULL - 模型名称
  - `status` - ENUM('pending','processing','completed','failed','cancelled') DEFAULT 'pending' - 任务状态
  - `input_data` - JSON NULL - 输入数据
  - `output_data` - JSON NULL - 输出数据
  - `generation_params` - JSON NULL - 生成参数
  - `external_task_id` - VARCHAR(255) NULL - 外部任务ID（AI平台返回的任务ID）
  - `cost` - DECIMAL(8,4) DEFAULT 0.0000 - 任务成本
  - `tokens_used` - INT DEFAULT 0 - 使用的令牌数
  - `processing_time_ms` - INT NULL - 处理时间（毫秒）
  - `started_at` - TIMESTAMP NULL - 开始时间
  - `completed_at` - TIMESTAMP NULL - 完成时间
  - `error_message` - TEXT NULL - 错误信息
  - `metadata` - JSON NULL - 任务元数据
  - `retry_count` - INT DEFAULT 0 - 重试次数
  - `max_retries` - INT DEFAULT 3 - 最大重试次数
  - `created_at` - TIMESTAMP DEFAULT CURRENT_TIMESTAMP - 创建时间
  - `updated_at` - TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP - 更新时间

- **p_websocket_sessions**: WebSocket会话表（连接管理、状态同步）
  - `session_id` - 会话ID
  - `user_id` - 用户ID，关联p_users表
  - `connection_id` - 连接ID
  - `client_type` - 客户端类型（py_tool/web/admin）
  - `client_version` - 客户端版本
  - `ip_address` - IP地址
  - `user_agent` - 用户代理
  - `status` - 连接状态（connected/disconnected/error）
  - `last_ping_at` - 最后心跳时间
  - `connected_at` - 连接时间
  - `disconnected_at` - 断开时间
  - `metadata` - 会话元数据（JSON格式）
  - `created_at` - 创建时间
  - `updated_at` - 更新时间

### 用户作品管理表
- **p_user_works**: 用户作品表（用户创作的作品管理）
  - `user_id` - 用户ID，关联p_users表
  - `work_title` - 作品标题
  - `work_type` - 作品类型（video/image/music/story/character）
  - `content` - 作品内容或描述
  - `project_id` - 关联的项目ID（可选）
  - `status` - 作品状态（draft/completed/published/archived）
  - `visibility` - 可见性（private/public/friends_only）
  - `metadata` - 作品元数据（JSON格式，包含文件路径、尺寸、时长等）
  - `tags` - 作品标签（JSON数组）
  - `view_count` - 浏览次数
  - `like_count` - 点赞数量
  - `comment_count` - 评论数量
  - `share_count` - 分享次数
  - `featured_at` - 精选时间（用于推荐算法）
  - `published_at` - 发布时间
  - `created_at` - 创建时间
  - `updated_at` - 更新时间

### 作品发布和社交表
- **p_work_plaza**: 作品广场表（公开作品展示平台）
  - `work_id` - 关联p_user_works表的作品ID
  - `user_id` - 作品创作者ID，关联p_users表
  - `title` - 作品展示标题
  - `description` - 作品描述
  - `category` - 作品分类（video/image/music/story/character/mixed）
  - `tags` - 作品标签（JSON数组，用于搜索和推荐）
  - `thumbnail` - 作品缩略图URL
  - `preview_url` - 预览文件URL（如视频预览、音频试听等）
  - `view_count` - 浏览次数
  - `like_count` - 点赞数量
  - `comment_count` - 评论数量
  - `share_count` - 分享次数
  - `download_count` - 下载次数
  - `rating` - 作品评分（1-5星）
  - `rating_count` - 评分人数
  - `review_status` - 审核状态（pending/approved/rejected/auto_approved）
  - `review_reason` - 审核说明
  - `featured` - 是否精选推荐
  - `featured_at` - 精选时间
  - `published_at` - 发布到广场的时间
  - `created_at` - 创建时间
  - `updated_at` - 更新时间

- **p_work_shares**: 作品分享表（分享链接、权限控制、访问统计）

### 社交功能表
- **p_follows**: 关注关系表（用户关注系统）
  - `id` - BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY - 主键ID
  - `follower_id` - BIGINT UNSIGNED NOT NULL - 关注者用户ID，关联p_users表
  - `following_id` - BIGINT UNSIGNED NOT NULL - 被关注者用户ID，关联p_users表
  - `status` - ENUM('active','blocked','pending') DEFAULT 'active' - 关注状态
  - `notification_enabled` - BOOLEAN DEFAULT TRUE - 是否启用通知
  - `created_at` - TIMESTAMP DEFAULT CURRENT_TIMESTAMP - 关注时间
  - `updated_at` - TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP - 更新时间

- **p_likes**: 点赞表（通用点赞系统）
  - `id` - BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY - 主键ID
  - `user_id` - BIGINT UNSIGNED NOT NULL - 点赞用户ID，关联p_users表
  - `target_type` - VARCHAR(50) NOT NULL - 目标类型（work/comment/user/project）
  - `target_id` - BIGINT UNSIGNED NOT NULL - 目标ID（根据target_type关联不同表）
  - `created_at` - TIMESTAMP DEFAULT CURRENT_TIMESTAMP - 点赞时间

- **p_comments**: 评论表（通用评论系统）
  - `id` - BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY - 主键ID
  - `user_id` - BIGINT UNSIGNED NOT NULL - 评论用户ID，关联p_users表
  - `target_type` - VARCHAR(50) NOT NULL - 评论目标类型（work/project/user）
  - `target_id` - BIGINT UNSIGNED NOT NULL - 评论目标ID（根据target_type关联不同表）
  - `content` - TEXT NOT NULL - 评论内容
  - `parent_id` - BIGINT UNSIGNED NULL - 父评论ID（用于回复功能，关联本表）
  - `status` - ENUM('active','hidden','deleted','pending_review') DEFAULT 'active' - 评论状态
  - `like_count` - INT DEFAULT 0 - 评论点赞数
  - `reply_count` - INT DEFAULT 0 - 回复数量
  - `is_pinned` - BOOLEAN DEFAULT FALSE - 是否置顶
  - `created_at` - TIMESTAMP DEFAULT CURRENT_TIMESTAMP - 评论时间
  - `updated_at` - TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP - 更新时间

- **p_work_interactions**: 作品互动表（点赞、评论、分享记录的汇总统计）
  - `id` - BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY - 主键ID
  - `work_id` - BIGINT UNSIGNED NOT NULL - 作品ID，关联p_user_works表
  - `user_id` - BIGINT UNSIGNED NOT NULL - 用户ID，关联p_users表
  - `interaction_type` - ENUM('like','comment','share','view','download') NOT NULL - 互动类型
  - `interaction_data` - JSON NULL - 互动数据
  - `interaction_metadata` - JSON NULL - 互动元数据
  - `created_at` - TIMESTAMP DEFAULT CURRENT_TIMESTAMP - 创建时间

### 项目管理表
- **p_projects**: 项目表（视频创作项目管理）
  - `user_id` - 用户ID，关联p_users表
  - `project_name` - 项目名称
  - `description` - 项目描述
  - `project_type` - 项目类型（video/image/music/story/mixed）
  - `status` - 项目状态（draft/in_progress/completed/published/archived）
  - `visibility` - 可见性（private/public/team）
  - `settings` - 项目设置（JSON格式）
  - `metadata` - 项目元数据（JSON格式）
  - `thumbnail` - 项目缩略图
  - `progress` - 完成进度（0-100）
  - `estimated_duration` - 预计时长（分钟）
  - `actual_duration` - 实际时长（分钟）
  - `tags` - 项目标签（JSON数组）
  - `collaborator_count` - 协作者数量
  - `resource_count` - 资源数量
  - `last_activity_at` - 最后活动时间
  - `published_at` - 发布时间
  - `created_at` - 创建时间
  - `updated_at` - 更新时间

- **p_project_collaborators**: 项目协作者表（项目协作管理）
  - `project_id` - 项目ID，关联p_projects表
  - `user_id` - 用户ID，关联p_users表
  - `role` - 协作角色（owner/editor/viewer/reviewer）
  - `permissions` - 权限设置（JSON数组）
  - `status` - 协作状态（active/inactive/pending/rejected）
  - `invited_by` - 邀请人ID，关联p_users表
  - `invited_at` - 邀请时间
  - `joined_at` - 加入时间
  - `last_activity_at` - 最后活动时间
  - `created_at` - 创建时间
  - `updated_at` - 更新时间

- **p_project_storyboards**: 项目分镜表（分镜详细信息、动作库关联）
  - `id` - BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY - 主键ID
  - `project_id` - BIGINT UNSIGNED NOT NULL - 项目ID，关联p_projects表
  - `scene_number` - INT NOT NULL - 分镜序号
  - `scene_title` - VARCHAR(200) NOT NULL - 分镜标题
  - `scene_description` - TEXT NOT NULL - 分镜描述
  - `camera_angle` - VARCHAR(50) NULL - 镜头角度
  - `shot_type` - VARCHAR(50) NULL - 镜头类型
  - `lighting` - VARCHAR(100) NULL - 光线设置
  - `background_description` - TEXT NULL - 背景描述
  - `ai_prompt` - TEXT NULL - AI生成提示词
  - `generation_params` - JSON NULL - 生成参数
  - `style_overrides` - JSON NULL - 风格覆盖设置
  - `duration` - INT NULL - 预计时长（秒）
  - `status` - ENUM('draft','approved','generating','completed','failed') DEFAULT 'draft' - 分镜状态
  - `generated_image_id` - BIGINT UNSIGNED NULL - 生成的图片资源ID，关联p_resources表
  - `generated_video_id` - BIGINT UNSIGNED NULL - 生成的视频资源ID，关联p_resources表
  - `action_library_id` - BIGINT UNSIGNED NULL - 分镜动作库ID，关联p_storyboard_action_library表
  - `metadata` - JSON NULL - 分镜元数据
  - `created_at` - TIMESTAMP DEFAULT CURRENT_TIMESTAMP - 创建时间
  - `updated_at` - TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP - 更新时间
  - **索引**:
    - INDEX `idx_project_scene` (`project_id`, `scene_number`)
    - INDEX `idx_project_status` (`project_id`, `status`)
    - INDEX `idx_status` (`status`)
    - INDEX `idx_action_library_id` (`action_library_id`)
  - **外键约束**:
    - FOREIGN KEY (`project_id`) REFERENCES `p_projects`(`id`) ON DELETE CASCADE
    - FOREIGN KEY (`generated_image_id`) REFERENCES `p_resources`(`id`) ON DELETE SET NULL
    - FOREIGN KEY (`generated_video_id`) REFERENCES `p_resources`(`id`) ON DELETE SET NULL
    - FOREIGN KEY (`action_library_id`) REFERENCES `p_storyboard_action_library`(`id`) ON DELETE SET NULL
  - **唯一约束**:
    - UNIQUE `unique_project_scene` (`project_id`, `scene_number`)

- **p_storyboard_narrations**: 分镜旁白/解说表（音色试听流程支持）
  - `id` - BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY - 主键ID
  - `storyboard_id` - BIGINT UNSIGNED NOT NULL - 分镜ID，关联p_project_storyboards表
  - `narrator_character_id` - BIGINT UNSIGNED NULL - 解说角色ID，关联p_project_characters表
  - `voice_id` - VARCHAR(100) NULL - 音色ID
  - `platform` - VARCHAR(50) DEFAULT 'volcengine' - AI平台（volcengine/minimax等）
  - `language` - VARCHAR(10) DEFAULT 'zh-CN' - 语言（zh-CN/en-US/ja-JP/ko-KR）
  - `emotion` - VARCHAR(50) DEFAULT 'neutral' - 情绪（neutral/happy/sad/angry/excited/calm/energetic）
  - `speed` - DECIMAL(3,1) DEFAULT 1.0 - 语速（0.5-2.0）
  - `pitch` - DECIMAL(3,1) DEFAULT 1.0 - 音调（0.5-2.0）
  - `subtitle_text` - TEXT NULL - 旁白文本（可选，默认沿用分镜）
  - `status` - ENUM('active','inactive') DEFAULT 'active' - 状态
  - `last_preview_url` - VARCHAR(500) NULL - 最后试听URL（便于前端复用）
  - `created_at` - TIMESTAMP DEFAULT CURRENT_TIMESTAMP - 创建时间
  - `updated_at` - TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP - 更新时间
  - **索引**:
    - INDEX `idx_storyboard_id` (`storyboard_id`)
    - INDEX `idx_narrator_character_id` (`narrator_character_id`)
    - INDEX `idx_voice_platform` (`voice_id`, `platform`)
  - **外键约束**:
    - FOREIGN KEY (`storyboard_id`) REFERENCES `p_project_storyboards`(`id`) ON DELETE CASCADE
    - FOREIGN KEY (`narrator_character_id`) REFERENCES `p_project_characters`(`id`) ON DELETE SET NULL

### 用户成长系统表
- **p_user_levels**: 用户等级表（等级系统配置）
  - `level` - 等级数值
  - `level_name` - 等级名称
  - `required_experience` - 所需经验值
  - `level_benefits` - 等级权益（JSON格式）
  - `level_icon` - 等级图标
  - `level_color` - 等级颜色
  - `description` - 等级描述
  - `is_active` - 是否激活
  - `created_at` - 创建时间
  - `updated_at` - 更新时间

- **p_achievements**: 成就表（成就系统）
  - `name` - 成就名称
  - `description` - 成就描述
  - `type` - 成就类型（daily/weekly/monthly/milestone/special/social/creative/learning）
  - `difficulty` - 难度等级（easy/medium/hard/legendary）
  - `icon` - 成就图标
  - `badge_color` - 徽章颜色
  - `requirements` - 完成条件（JSON格式）
  - `reward_experience` - 奖励经验值
  - `reward_points` - 奖励积分
  - `status` - 成就状态（active/inactive/draft）
  - `sort_order` - 排序权重
  - `is_hidden` - 是否隐藏成就
  - `unlock_condition` - 解锁条件（JSON格式）
  - `metadata` - 成就元数据（JSON格式）
  - `created_at` - 创建时间
  - `updated_at` - 更新时间

- **p_user_achievements**: 用户成就表（用户成就记录）
  - `user_id` - 用户ID，关联p_users表
  - `achievement_id` - 成就ID，关联p_achievements表
  - `progress` - 完成进度（0-100）
  - `completed_at` - 完成时间
  - `notified_at` - 通知时间
  - `metadata` - 成就元数据（JSON格式）
  - `created_at` - 创建时间
  - `updated_at` - 更新时间

- **p_daily_tasks**: 每日任务表（每日任务系统）
  - `name` - 任务名称
  - `description` - 任务描述
  - `task_type` - 任务类型（login/create/share/interact/learn）
  - `difficulty` - 难度等级（easy/medium/hard）
  - `target_value` - 目标数值
  - `reward_experience` - 奖励经验值
  - `reward_points` - 奖励积分
  - `is_active` - 是否激活
  - `sort_order` - 排序权重
  - `requirements` - 完成条件（JSON格式）
  - `metadata` - 任务元数据（JSON格式）
  - `created_at` - 创建时间
  - `updated_at` - 更新时间

- **p_user_daily_tasks**: 用户每日任务表（用户每日任务记录）
  - `user_id` - 用户ID，关联p_users表
  - `daily_task_id` - 每日任务ID，关联p_daily_tasks表
  - `task_date` - 任务日期
  - `progress` - 完成进度
  - `target_value` - 目标数值
  - `current_value` - 当前数值
  - `status` - 任务状态（pending/in_progress/completed/expired）
  - `completed_at` - 完成时间
  - `reward_claimed` - 是否已领取奖励
  - `claimed_at` - 领取时间
  - `metadata` - 任务元数据（JSON格式）
  - `created_at` - 创建时间
  - `updated_at` - 更新时间

- **p_growth_histories**: 用户成长历史表（成长轨迹记录）
  - `user_id` - 用户ID，关联p_users表
  - `event_type` - 事件类型（level_up/achievement/task_complete/milestone）
  - `event_description` - 事件描述
  - `experience_gained` - 获得经验值
  - `points_gained` - 获得积分
  - `level_before` - 事件前等级
  - `level_after` - 事件后等级
  - `experience_before` - 事件前经验值
  - `experience_after` - 事件后经验值
  - `related_id` - 关联ID（成就ID、任务ID等）
  - `related_type` - 关联类型
  - `metadata` - 事件元数据（JSON格式）
  - `created_at` - 创建时间
  - `updated_at` - 更新时间

### 系统管理表
- **p_user_preferences**: 用户偏好设置表（个性化配置）
  - `user_id` - 用户ID，关联p_users表
  - `preference_key` - 偏好键名
  - `preference_value` - 偏好值（JSON格式）
  - `category` - 偏好分类（ui/notification/privacy/ai）
  - `is_default` - 是否默认值
  - `created_at` - 创建时间
  - `updated_at` - 更新时间

- **p_user_model_preferences**: 用户AI模型偏好表（AI模型选择偏好）
  - `id` - BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY - 主键ID
  - `user_id` - BIGINT UNSIGNED NOT NULL - 用户ID，关联p_users表
  - `task_type` - VARCHAR(50) NOT NULL - 任务类型（text/image/video/voice/music/sound）
  - `preferred_platform` - VARCHAR(50) NOT NULL - 偏好平台
  - `preferred_model` - VARCHAR(100) NOT NULL - 偏好模型
  - `preference_weight` - DECIMAL(3,2) DEFAULT 1.00 - 偏好权重（0-1）
  - `usage_count` - INT DEFAULT 0 - 使用次数
  - `success_rate` - DECIMAL(5,4) DEFAULT 0.0000 - 成功率（0-1）
  - `average_satisfaction` - DECIMAL(3,2) DEFAULT 0.00 - 平均满意度（0-5）
  - `last_used_at` - TIMESTAMP NULL - 最后使用时间
  - `is_active` - BOOLEAN DEFAULT TRUE - 是否激活
  - `metadata` - JSON NULL - 偏好元数据
  - `created_at` - TIMESTAMP DEFAULT CURRENT_TIMESTAMP - 创建时间
  - `updated_at` - TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP - 更新时间

- **p_platform_usage_statistics**: 平台使用统计表（AI平台使用数据）
  - `id` - BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY - 主键ID
  - `user_id` - BIGINT UNSIGNED NULL - 用户ID，关联p_users表
  - `platform` - VARCHAR(50) NOT NULL - AI平台标识
  - `model_name` - VARCHAR(100) NOT NULL - 模型名称
  - `task_type` - VARCHAR(50) NOT NULL - 任务类型
  - `usage_date` - DATE NOT NULL - 使用日期
  - `request_count` - INT DEFAULT 0 - 请求次数
  - `success_count` - INT DEFAULT 0 - 成功次数
  - `failure_count` - INT DEFAULT 0 - 失败次数
  - `total_cost` - DECIMAL(10,4) DEFAULT 0.0000 - 总成本
  - `total_tokens` - INT DEFAULT 0 - 总令牌数
  - `average_response_time` - INT DEFAULT 0 - 平均响应时间（毫秒）
  - `user_satisfaction_score` - DECIMAL(3,2) DEFAULT 0.00 - 用户满意度评分（1-5）
  - `metadata` - JSON NULL - 统计元数据
  - `created_at` - TIMESTAMP DEFAULT CURRENT_TIMESTAMP - 创建时间
  - `updated_at` - TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP - 更新时间

- **p_platform_performance_metrics**: 平台性能指标表（AI平台性能监控）
  - `platform` - AI平台标识
  - `model_name` - 模型名称
  - `metric_type` - 指标类型（response_time/success_rate/cost_efficiency/user_satisfaction）
  - `metric_value` - 指标值
  - `measurement_date` - 测量日期
  - `measurement_hour` - 测量小时
  - `sample_count` - 样本数量
  - `min_value` - 最小值
  - `max_value` - 最大值
  - `avg_value` - 平均值
  - `std_deviation` - 标准差
  - `percentile_95` - 95百分位数
  - `metadata` - 指标元数据（JSON格式）
  - `created_at` - 创建时间
  - `updated_at` - 更新时间

- **p_system_monitors**: 系统监控表（系统性能监控）
  - `metric_type` - 指标类型（cpu/memory/disk/network/database）
  - `metric_name` - 指标名称
  - `metric_value` - 指标值
  - `unit` - 单位
  - `threshold_warning` - 警告阈值
  - `threshold_critical` - 严重阈值
  - `status` - 状态（normal/warning/critical）
  - `server_name` - 服务器名称
  - `measurement_time` - 测量时间
  - `metadata` - 监控元数据（JSON格式）
  - `created_at` - 创建时间
  - `updated_at` - 更新时间

### 模板和文件管理表
- **p_templates**: 模板表（项目模板管理）
  - `user_id` - 用户ID，关联p_users表
  - `template_name` - 模板名称
  - `description` - 模板描述
  - `template_type` - 模板类型（video/image/music/story/mixed）
  - `category` - 模板分类
  - `template_data` - 模板数据（JSON格式）
  - `preview_image` - 预览图片
  - `is_public` - 是否公开
  - `is_featured` - 是否精选
  - `usage_count` - 使用次数
  - `rating` - 用户评分
  - `rating_count` - 评分人数
  - `tags` - 模板标签（JSON数组）
  - `difficulty_level` - 难度等级（beginner/intermediate/advanced）
  - `estimated_time` - 预计完成时间（分钟）
  - `status` - 模板状态（draft/published/archived）
  - `metadata` - 模板元数据（JSON格式）
  - `created_at` - 创建时间
  - `updated_at` - 更新时间

- **p_user_files**: 用户文件表（用户上传文件管理）
  - `user_id` - 用户ID，关联p_users表
  - `file_name` - 文件名称
  - `original_name` - 原始文件名
  - `file_path` - 文件路径
  - `file_size` - 文件大小（字节）
  - `file_type` - 文件类型（image/video/audio/document）
  - `mime_type` - MIME类型
  - `file_hash` - 文件哈希值
  - `upload_source` - 上传来源（web/py_tool/api）
  - `storage_type` - 存储类型（local/cloud/cdn）
  - `is_temporary` - 是否临时文件
  - `expires_at` - 过期时间
  - `download_count` - 下载次数
  - `last_accessed_at` - 最后访问时间
  - `metadata` - 文件元数据（JSON格式）
  - `status` - 文件状态（active/deleted/expired）
  - `created_at` - 创建时间
  - `updated_at` - 更新时间

### 资源导出和下载表
- **p_resource_downloads**: 资源下载记录表（下载历史追踪）
  - `user_id` - 用户ID，关联p_users表
  - `resource_id` - 资源ID，关联p_resources表
  - `download_type` - 下载类型（full/preview/thumbnail）
  - `download_source` - 下载来源（web/py_tool/api）
  - `file_size` - 文件大小
  - `download_speed` - 下载速度（KB/s）
  - `download_duration` - 下载时长（秒）
  - `ip_address` - IP地址
  - `user_agent` - 用户代理
  - `status` - 下载状态（completed/failed/cancelled）
  - `error_message` - 错误信息
  - `metadata` - 下载元数据（JSON格式）
  - `created_at` - 创建时间
  - `updated_at` - 更新时间

- **p_resource_exports**: 资源导出记录表（批量导出管理）
  - `user_id` - 用户ID，关联p_users表
  - `export_name` - 导出名称
  - `export_type` - 导出类型（project/resources/templates）
  - `resource_ids` - 资源ID列表（JSON数组）
  - `export_format` - 导出格式（zip/tar/json）
  - `export_settings` - 导出设置（JSON格式）
  - `file_path` - 导出文件路径
  - `file_size` - 文件大小
  - `compression_ratio` - 压缩比率
  - `status` - 导出状态（pending/processing/completed/failed/expired）
  - `progress` - 导出进度（0-100）
  - `started_at` - 开始时间
  - `completed_at` - 完成时间
  - `expires_at` - 过期时间
  - `download_count` - 下载次数
  - `error_message` - 错误信息
  - `metadata` - 导出元数据（JSON格式）
  - `created_at` - 创建时间
  - `updated_at` - 更新时间

### 内容发布和审核表
- **p_publications**: 内容发布表（内容发布管理）
  - `user_id` - 用户ID，关联p_users表
  - `title` - 发布标题
  - `content` - 发布内容
  - `content_type` - 内容类型（article/tutorial/showcase/announcement）
  - `category` - 内容分类
  - `tags` - 内容标签（JSON数组）
  - `featured_image` - 特色图片
  - `excerpt` - 内容摘要
  - `status` - 发布状态（draft/published/archived/deleted）
  - `visibility` - 可见性（public/private/members_only）
  - `is_featured` - 是否精选
  - `is_pinned` - 是否置顶
  - `view_count` - 浏览次数
  - `like_count` - 点赞数量
  - `comment_count` - 评论数量
  - `share_count` - 分享次数
  - `reading_time` - 预计阅读时间（分钟）
  - `seo_title` - SEO标题
  - `seo_description` - SEO描述
  - `seo_keywords` - SEO关键词
  - `published_at` - 发布时间
  - `featured_at` - 精选时间
  - `metadata` - 发布元数据（JSON格式）
  - `created_at` - 创建时间
  - `updated_at` - 更新时间

- **p_reviews**: 内容审核表（内容审核管理）
  - `publication_id` - 发布内容ID，关联p_publications表
  - `reviewer_id` - 审核员ID，关联p_users表
  - `review_type` - 审核类型（content/quality/policy/copyright）
  - `status` - 审核状态（pending/approved/rejected/needs_revision）
  - `score` - 审核评分（1-10）
  - `feedback` - 审核反馈
  - `review_notes` - 审核备注
  - `policy_violations` - 政策违规项（JSON数组）
  - `suggested_changes` - 建议修改（JSON格式）
  - `priority` - 审核优先级（low/medium/high/urgent）
  - `estimated_time` - 预计审核时间（分钟）
  - `actual_time` - 实际审核时间（分钟）
  - `reviewed_at` - 审核时间
  - `metadata` - 审核元数据（JSON格式）
  - `created_at` - 创建时间
  - `updated_at` - 更新时间

- **p_review_appeals**: 审核申诉表（审核申诉管理）
  - `review_id` - 审核ID，关联p_reviews表
  - `user_id` - 申诉用户ID，关联p_users表
  - `appeal_reason` - 申诉原因
  - `appeal_content` - 申诉内容
  - `supporting_evidence` - 支持证据（JSON格式）
  - `status` - 申诉状态（pending/accepted/rejected/withdrawn）
  - `handler_id` - 处理人ID，关联p_users表
  - `handler_notes` - 处理备注
  - `resolution` - 处理结果
  - `handled_at` - 处理时间
  - `metadata` - 申诉元数据（JSON格式）
  - `created_at` - 创建时间
  - `updated_at` - 更新时间

### 邀请佣金系统表
- **p_referral_codes**: 邀请码表（邀请推广系统）
  - `id` - BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY - 主键ID
  - `user_id` - BIGINT UNSIGNED NOT NULL - 用户ID，关联p_users表
  - `code` - VARCHAR(20) UNIQUE NOT NULL - 邀请码
  - `code_type` - ENUM('personal','campaign','event') DEFAULT 'personal' - 邀请码类型
  - `description` - VARCHAR(255) NULL - 邀请码描述
  - `max_uses` - INT DEFAULT 0 - 最大使用次数（0为无限制）
  - `used_count` - INT DEFAULT 0 - 已使用次数
  - `commission_rate` - DECIMAL(5,4) DEFAULT 0.0000 - 佣金比例（0-1）
  - `bonus_points` - DECIMAL(10,2) DEFAULT 0.00 - 奖励积分
  - `valid_from` - TIMESTAMP NULL - 有效开始时间
  - `valid_until` - TIMESTAMP NULL - 有效结束时间
  - `status` - ENUM('active','inactive','expired','disabled') DEFAULT 'active' - 状态
  - `metadata` - JSON NULL - 扩展信息
  - `created_at` - TIMESTAMP DEFAULT CURRENT_TIMESTAMP - 创建时间
  - `updated_at` - TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP - 更新时间

- **p_referral_commissions**: 邀请佣金表（佣金记录和结算）
  - `id` - BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY - 主键ID
  - `referrer_id` - BIGINT UNSIGNED NOT NULL - 推荐人ID，关联p_users表
  - `referee_id` - BIGINT UNSIGNED NOT NULL - 被推荐人ID，关联p_users表
  - `referral_code_id` - BIGINT UNSIGNED NOT NULL - 邀请码ID，关联p_referral_codes表
  - `commission_type` - ENUM('registration','first_purchase','consumption','milestone') NOT NULL - 佣金类型
  - `base_amount` - DECIMAL(10,2) NOT NULL - 基础金额
  - `commission_rate` - DECIMAL(5,4) NOT NULL - 佣金比例
  - `commission_amount` - DECIMAL(10,2) NOT NULL - 佣金金额
  - `bonus_points` - DECIMAL(10,2) DEFAULT 0.00 - 奖励积分
  - `status` - ENUM('pending','confirmed','paid','cancelled') DEFAULT 'pending' - 佣金状态
  - `settlement_date` - DATE NULL - 结算日期
  - `payment_method` - VARCHAR(50) NULL - 支付方式
  - `transaction_id` - VARCHAR(100) NULL - 交易ID
  - `notes` - TEXT NULL - 备注信息
  - `metadata` - JSON NULL - 扩展信息
  - `created_at` - TIMESTAMP DEFAULT CURRENT_TIMESTAMP - 创建时间
  - `updated_at` - TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP - 更新时间

### 用户关系和绑定表
- **p_user_character_bindings**: 用户角色绑定表（用户与角色的绑定关系）
  - `user_id` - 用户ID，关联p_users表
  - `character_id` - 角色ID，关联p_character_library表
  - `binding_type` - 绑定类型（favorite/owned/created/customized）
  - `custom_settings` - 自定义设置（JSON格式）
  - `usage_count` - 使用次数
  - `last_used_at` - 最后使用时间
  - `is_active` - 是否激活
  - `notes` - 用户备注
  - `metadata` - 绑定元数据（JSON格式）
  - `created_at` - 创建时间
  - `updated_at` - 更新时间

### 下载推广系统表
- **p_download_tracking**: 终端下载推广系统（下载来源追踪）
  - `id` - BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY - 主键ID
  - `tracking_code` - VARCHAR(50) UNIQUE NOT NULL - 追踪码
  - `referrer_id` - BIGINT UNSIGNED NULL - 推荐人ID，关联p_users表
  - `campaign_name` - VARCHAR(100) NULL - 推广活动名称
  - `source` - VARCHAR(50) NULL - 来源渠道（website/social/email/ad等）
  - `medium` - VARCHAR(50) NULL - 媒介类型（organic/cpc/banner/referral等）
  - `platform` - VARCHAR(50) NULL - 下载平台（windows/mac/android/ios）
  - `version` - VARCHAR(20) NULL - 下载版本
  - `ip_address` - VARCHAR(45) NULL - 下载IP地址
  - `user_agent` - TEXT NULL - 用户代理信息
  - `country` - VARCHAR(50) NULL - 国家
  - `region` - VARCHAR(50) NULL - 地区
  - `city` - VARCHAR(50) NULL - 城市
  - `download_time` - TIMESTAMP NULL - 下载时间
  - `install_confirmed` - BOOLEAN DEFAULT FALSE - 是否确认安装
  - `first_launch_time` - TIMESTAMP NULL - 首次启动时间
  - `user_registered` - BOOLEAN DEFAULT FALSE - 是否注册用户
  - `registration_time` - TIMESTAMP NULL - 注册时间
  - `conversion_value` - DECIMAL(10,2) DEFAULT 0.00 - 转化价值
  - `status` - ENUM('downloaded','installed','registered','active') DEFAULT 'downloaded' - 状态
  - `metadata` - JSON NULL - 扩展信息
  - `created_at` - TIMESTAMP DEFAULT CURRENT_TIMESTAMP - 创建时间
  - `updated_at` - TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP - 更新时间

### 缓存管理表
- **p_cache_statistics**: 缓存管理和性能优化系统（缓存使用统计）
  - `id` - BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY - 主键ID
  - `cache_key` - VARCHAR(255) NOT NULL - 缓存键名
  - `cache_type` - VARCHAR(50) NOT NULL - 缓存类型（redis/memory/file/database）
  - `cache_group` - VARCHAR(50) NULL - 缓存分组
  - `hit_count` - BIGINT UNSIGNED DEFAULT 0 - 命中次数
  - `miss_count` - BIGINT UNSIGNED DEFAULT 0 - 未命中次数
  - `total_requests` - BIGINT UNSIGNED DEFAULT 0 - 总请求次数
  - `hit_rate` - DECIMAL(5,4) DEFAULT 0.0000 - 命中率
  - `avg_response_time` - DECIMAL(8,3) DEFAULT 0.000 - 平均响应时间（毫秒）
  - `data_size` - BIGINT UNSIGNED DEFAULT 0 - 数据大小（字节）
  - `ttl_seconds` - INT NULL - 生存时间（秒）
  - `last_accessed` - TIMESTAMP NULL - 最后访问时间
  - `last_updated` - TIMESTAMP NULL - 最后更新时间
  - `expiry_time` - TIMESTAMP NULL - 过期时间
  - `access_pattern` - JSON NULL - 访问模式统计
  - `performance_metrics` - JSON NULL - 性能指标
  - `status` - ENUM('active','expired','evicted','error') DEFAULT 'active' - 缓存状态
  - `created_at` - TIMESTAMP DEFAULT CURRENT_TIMESTAMP - 创建时间
  - `updated_at` - TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP - 更新时间

### 推荐系统表
- **p_recommendations**: 推荐表（个性化推荐系统）
  - `id` - BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY - 主键ID
  - `user_id` - BIGINT UNSIGNED NOT NULL - 用户ID，关联p_users表
  - `item_type` - VARCHAR(50) NOT NULL - 推荐项类型（character/style/template/work/user）
  - `item_id` - BIGINT UNSIGNED NOT NULL - 推荐项ID
  - `recommendation_type` - VARCHAR(50) NOT NULL - 推荐类型（collaborative/content_based/hybrid/trending）
  - `score` - DECIMAL(5,4) NOT NULL - 推荐分数（0-1）
  - `reason` - VARCHAR(255) NULL - 推荐原因
  - `context` - JSON NULL - 推荐上下文
  - `status` - ENUM('active','clicked','dismissed','expired') DEFAULT 'active' - 推荐状态
  - `clicked_at` - TIMESTAMP NULL - 点击时间
  - `dismissed_at` - TIMESTAMP NULL - 忽略时间
  - `expires_at` - TIMESTAMP NULL - 过期时间
  - `metadata` - JSON NULL - 推荐元数据
  - `created_at` - TIMESTAMP DEFAULT CURRENT_TIMESTAMP - 创建时间
  - `updated_at` - TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP - 更新时间

- **p_recommendation_feedbacks**: 推荐反馈表（推荐效果反馈）
  - `id` - BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY - 主键ID
  - `user_id` - BIGINT UNSIGNED NOT NULL - 用户ID，关联p_users表
  - `recommendation_id` - BIGINT UNSIGNED NOT NULL - 推荐ID，关联p_recommendations表
  - `feedback_type` - ENUM('like','dislike','not_interested','irrelevant') NOT NULL - 反馈类型
  - `feedback_score` - INT NULL - 反馈评分（1-5）
  - `feedback_reason` - VARCHAR(255) NULL - 反馈原因
  - `feedback_text` - TEXT NULL - 反馈文本
  - `context_data` - JSON NULL - 上下文数据
  - `metadata` - JSON NULL - 反馈元数据
  - `created_at` - TIMESTAMP DEFAULT CURRENT_TIMESTAMP - 创建时间
  - `updated_at` - TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP - 更新时间

### 工作流管理系统表
- **p_workflow_templates**: 工作流模板表（工作流模板定义）
  - `id` - BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY - 主键ID
  - `name` - VARCHAR(255) NOT NULL - 模板名称
  - `description` - TEXT NULL - 模板描述
  - `category` - VARCHAR(50) NULL - 模板分类（video_production/content_creation/automation）
  - `workflow_type` - VARCHAR(50) NOT NULL - 工作流类型（sequential/parallel/conditional）
  - `version` - VARCHAR(20) DEFAULT '1.0' - 模板版本
  - `definition` - JSON NOT NULL - 工作流定义（节点、连接、配置）
  - `input_schema` - JSON NULL - 输入参数模式
  - `output_schema` - JSON NULL - 输出结果模式
  - `default_config` - JSON NULL - 默认配置
  - `estimated_duration` - INT NULL - 预计执行时间（分钟）
  - `complexity_level` - ENUM('simple','medium','complex','expert') DEFAULT 'medium' - 复杂度等级
  - `tags` - JSON NULL - 模板标签数组
  - `is_public` - BOOLEAN DEFAULT FALSE - 是否公开模板
  - `is_featured` - BOOLEAN DEFAULT FALSE - 是否精选模板
  - `usage_count` - INT DEFAULT 0 - 使用次数
  - `rating` - DECIMAL(3,2) DEFAULT 0.00 - 用户评分（0-5）
  - `rating_count` - INT DEFAULT 0 - 评分人数
  - `created_by` - BIGINT UNSIGNED NOT NULL - 创建者ID，关联p_users表
  - `status` - ENUM('draft','published','archived','deprecated') DEFAULT 'draft' - 模板状态
  - `created_at` - TIMESTAMP DEFAULT CURRENT_TIMESTAMP - 创建时间
  - `updated_at` - TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP - 更新时间

- **p_workflow_executions**: 工作流执行表（工作流执行记录）
  - `id` - BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY - 主键ID
  - `user_id` - BIGINT UNSIGNED NOT NULL - 用户ID，关联p_users表
  - `template_id` - BIGINT UNSIGNED NOT NULL - 模板ID，关联p_workflow_templates表
  - `project_id` - BIGINT UNSIGNED NULL - 项目ID，关联p_projects表
  - `execution_name` - VARCHAR(255) NULL - 执行名称
  - `input_data` - JSON NULL - 输入数据
  - `output_data` - JSON NULL - 输出数据
  - `config_overrides` - JSON NULL - 配置覆盖
  - `execution_plan` - JSON NULL - 执行计划
  - `current_step` - VARCHAR(100) NULL - 当前步骤
  - `completed_steps` - JSON NULL - 已完成步骤数组
  - `failed_steps` - JSON NULL - 失败步骤数组
  - `progress_percentage` - DECIMAL(5,2) DEFAULT 0.00 - 进度百分比（0-100）
  - `status` - ENUM('pending','running','paused','completed','failed','cancelled') DEFAULT 'pending' - 执行状态
  - `priority` - ENUM('low','normal','high','urgent') DEFAULT 'normal' - 执行优先级
  - `started_at` - TIMESTAMP NULL - 开始时间
  - `completed_at` - TIMESTAMP NULL - 完成时间
  - `duration_seconds` - INT NULL - 执行时长（秒）
  - `error_message` - TEXT NULL - 错误信息
  - `retry_count` - INT DEFAULT 0 - 重试次数
  - `max_retries` - INT DEFAULT 3 - 最大重试次数
  - `logs` - JSON NULL - 执行日志
  - `metadata` - JSON NULL - 执行元数据
  - `created_at` - TIMESTAMP DEFAULT CURRENT_TIMESTAMP - 创建时间
  - `updated_at` - TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP - 更新时间

## 数据表关系说明

### 核心关系链
1. **用户 → 项目 → 资源 → 版本**: `p_users` → `p_projects` → `p_resources` → `p_resource_versions`
2. **用户 → 积分交易 → 积分冻结**: `p_users` → `p_points_transactions` → `p_points_freeze`
3. **用户 → AI任务 → 模型配置**: `p_users` → `p_ai_generation_tasks` → `p_ai_model_configs`
4. **用户 → 作品 → 作品广场**: `p_users` → `p_user_works` → `p_work_plaza`
5. **用户 → 成就系统**: `p_users` → `p_user_achievements` → `p_achievements`

### 🔍 索引建议
- **高频查询字段**: `user_id`, `project_id`, `resource_id`, `status`, `created_at`
- **复合索引**: `(user_id, status)`, `(project_id, resource_type)`, `(platform, model_type)`
- **唯一索引**: `resource_uuid`, `session_id`, `username`, `email`
- **外键索引**: 所有外键字段自动创建索引
- **业务索引**: 根据具体查询需求创建的功能性索引

### 🛡️ 数据一致性约束
- **外键约束**: 所有关联外键必须设置适当的约束和级联规则
- **枚举约束**: 状态字段使用ENUM类型确保数据一致性
- **JSON验证**: JSON字段需要设置合理的验证规则
- **时区统一**: 时间字段统一使用UTC时区
- **软删除**: 重要数据表支持软删除机制
- **字符集**: 统一使用utf8mb4字符集支持emoji

### 📊 表结构统计
- **总表数量**: 51个数据表 (已实现41个 + 规划新增10个)
- **核心业务表**: 3个 (用户、积分交易、积分冻结)
- **AI生成表**: 9个 (模型配置、风格库、角色库、角色分类、音乐库、音效库、音色库、故事库、分镜动作库)
- **资源管理表**: 4个 (资源、版本、下载、导出)
- **任务管理表**: 2个 (AI任务、WebSocket会话)
- **项目管理表**: 3个 (项目、协作者、分镜)
- **用户成长表**: 6个 (等级、成就、任务等)
- **系统管理表**: 6个 (偏好、统计、监控、缓存等)
- **社交功能表**: 6个 (关注、点赞、评论等)
- **邀请佣金表**: 2个 (邀请码、佣金记录)
- **下载推广表**: 1个 (下载追踪)
- **工作流管理表**: 2个 (工作流模板、执行记录)
- **其他功能表**: 7个 (模板、文件、发布、审核等)

### 🆕 新增规划表说明
**已规划但未实现的表** (10个):
- `p_music_library` - 音乐库表（AI生成音乐存储）
- `p_sound_library` - 音效库表（AI生成音效存储）
- `p_timbre_library` - 音色库表（AI生成音色存储）
- `p_story_library` - 故事库表（AI生成故事内容）
- `p_referral_codes` - 邀请码表（邀请推广系统）
- `p_referral_commissions` - 邀请佣金表（佣金记录和结算）
- `p_download_tracking` - 下载追踪表（终端下载推广）
- `p_cache_statistics` - 缓存统计表（缓存管理和性能优化）
- `p_workflow_templates` - 工作流模板表（工作流模板定义）
- `p_workflow_executions` - 工作流执行表（工作流执行记录）
